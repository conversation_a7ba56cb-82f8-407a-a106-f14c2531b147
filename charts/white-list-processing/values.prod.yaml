# Production environment specific values for white-list-processing

replicaCount: 1

resources:
  limits:
    cpu: 1000m
    memory: 1024Mi
  requests:
    cpu: 100m
    memory: 128Mi
env:
  client:
    logging:
      enable: true
  kafka:
    servers: "kafka1-prod-new.tkp2.prod:9092;kafka2-prod-new.tkp2.prod:9092;kafka3-prod-new.tkp2.prod:9092"
  service:
    emv_emission: "emv-emission.emv-prod.svc.cluster.local:5000"
    qr_emission: "qr-emission.qr-prod.svc.cluster.local:5000"
  develop:
    whitelist_lib_enable_log: true
