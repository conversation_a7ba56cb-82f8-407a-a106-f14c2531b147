apiVersion: v1
kind: Service
metadata:
  name: {{ include "white-list-processing.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "white-list-processing.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.http.type }}
  ports:
    - name: http
      port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
  selector:
    {{- include "white-list-processing.selectorLabels" . | nindent 4 }}
