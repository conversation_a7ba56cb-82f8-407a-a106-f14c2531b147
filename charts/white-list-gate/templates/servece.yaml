apiVersion: v1
kind: Service
metadata:
  name: {{ include "white-list-gate.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "white-list-gate.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.grpc.type }}
  ports:
    - name: http
      port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
    - name: grpc
      port: {{ .Values.service.grpc.port }}
      targetPort: {{ .Values.service.grpc.targetPort }}
      protocol: TCP
  selector:
    {{- include "white-list-gate.selectorLabels" . | nindent 4 }}
