package ru.sbertroika.tkp3.whitelist.gate.input

import org.lognet.springboot.grpc.GRpcService
import ru.sbertroika.common.ServiceError
import ru.sbertroika.common.toOperationError
import ru.sbertroika.tkp3.whitelist.gate.service.WhiteListService
import ru.sbertroika.white.list.gate.v1.WhiteListGateServiceGrpcKt
import ru.sbertroika.white.list.gate.v1.WhiteListUpdateRequest
import ru.sbertroika.white.list.gate.v1.WhiteListUpdateResponse
import ru.sbertroika.white.list.gate.v1.whiteListUpdateResponse

@GRpcService
class WhiteListGateServiceGrpc(
    private val whiteListService: WhiteListService
) : WhiteListGateServiceGrpcKt.WhiteListGateServiceCoroutineImplBase() {

    override suspend fun getWhiteListUpdate(request: WhiteListUpdateRequest): WhiteListUpdateResponse {
        return whiteListService.findWhiteListUpdate(request).fold(
            {
                whiteListUpdateResponse {
                    toOperationError(ServiceError(it.message))
                }
            },
            { update ->
                whiteListUpdateResponse {
                    result = update
                }
            }
        )
    }
}