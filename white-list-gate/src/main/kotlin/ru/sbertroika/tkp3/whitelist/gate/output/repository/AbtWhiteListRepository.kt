package ru.sbertroika.tkp3.whitelist.gate.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.white.list.model.db.AbtWhiteList
import java.util.*

interface AbtWhiteListRepository : CoroutineCrudRepository<AbtWhiteList, UUID> {

    @Query("SELECT * from abt_white_list where project_id = :projectId and type = :type and hash_type = :hashType order by version desc limit 1")
    suspend fun findLast(projectId: UUID, type: String, hashType: Int): AbtWhiteList?
    
    @Query("SELECT * from abt_white_list where project_id = :projectId and type = :type order by version desc")
    suspend fun findByProjectAndType(projectId: UUID, type: String): List<AbtWhiteList>
    
    @Query("SELECT * from abt_white_list where type = :type order by version desc limit 1")
    suspend fun findTopByOrderByVersionDesc(): AbtWhiteList?
    
    // Новые методы для поиска всех подтипов
    @Query("SELECT * from abt_white_list where project_id = :projectId and version > :version order by version asc, type asc, hash_type asc")
    suspend fun findLastByProjectAndVersion(projectId: UUID, version: Long): List<AbtWhiteList>
    
    @Query("SELECT * from abt_white_list where project_id = :projectId order by version desc, type asc, hash_type asc")
    suspend fun findMaxByProject(projectId: UUID): List<AbtWhiteList>
    
    @Query("SELECT * from abt_white_list where project_id = :projectId and type = :type and hash_type = :hashType and version > :version order by version asc limit 5")
    suspend fun findLastByProjectTypeHashTypeAndVersion(projectId: UUID, type: String, hashType: Int, version: Long): List<AbtWhiteList>
    
    // Эффективный метод для получения максимальных версий всех подтипов для проекта
    @Query("""
        SELECT DISTINCT ON (type, hash_type) *
        FROM abt_white_list 
        WHERE project_id = :projectId 
        ORDER BY type, hash_type, version DESC
    """)
    suspend fun findMaxVersionsByProject(projectId: UUID): List<AbtWhiteList>
} 