package ru.sbertroika.tkp3.whitelist.gate.input

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import ru.sbertroika.tkp3.whitelist.gate.service.WhiteListFileService


@RestController
class WhiteListGateController(
    @Value("\${input_log.enabled}")
    private var inputLog: Boolean,

    private val whiteListFileService: WhiteListFileService
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    @GetMapping("/wl/file/{file}")
    suspend fun getWlFile(@PathVariable file: String): Mono<ResponseEntity<Resource>> {
        if (inputLog) {
            log.info("REQUEST /getWlFile file=$file")
        }

        return whiteListFileService.getFile(file).fold(
            {
                log.error("Error get file", it)
                ResponseEntity(HttpStatus.NOT_FOUND)
            },
            { responseStream ->
                val resource = InputStreamResource(responseStream)
                val headers = HttpHeaders()

                headers.add(HttpHeaders.CONTENT_DISPOSITION, ("attachment; filename=\"" + file.substring(file.lastIndexOf('/') + 1)).toString() + "\"")

                ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(responseStream.response().contentLength())
                    .contentType(MediaType.parseMediaType(responseStream.response().contentType()))
                    .body<Resource>(resource)
            }
        ).toMono()
    }
}