package ru.sbertroika.tkp3.whitelist.gate.util

import ru.sbertroika.common.white.list.WhiteList
import ru.sbertroika.common.white.list.WhiteListUpdateType
import ru.sbertroika.tkp3.whitelist.api.model.HashType
import ru.sbertroika.tkp3.whitelist.api.model.SubscriptionType
import ru.sbertroika.white.list.model.db.AbtWhiteList

/**
 * Утилиты для управления версиями белых списков
 */
object WhiteListVersionManager {
    
    /**
     * Создает список обновлений для белого списка с учетом версий и типов файлов
     */
    fun createWhiteListUpdates(
        whiteListRecords: List<AbtWhiteList>,
        baseUrl: String,
        version: Long
    ): List<WhiteList> {
        val whiteLists = mutableListOf<WhiteList>()
        
        // Группируем записи по подтипам для избежания дублирования
        val recordsByType = whiteListRecords.groupBy { 
            Pair(it.type ?: "ABT_TICKET", it.hashType ?: 0) 
        }
        
        recordsByType.forEach { (typeHashPair, records) ->
            val subType = WhiteListTypeConverter.toWhiteListSubType(
                SubscriptionType.valueOf(typeHashPair.first),
                HashType.values()[typeHashPair.second]
            )
            
            if (version == 0L) {
                // Если version = 0, отдаем только full файлы максимальной версии
                val maxVersionRecord = records.maxByOrNull { it.version ?: 0L }
                if (maxVersionRecord != null && maxVersionRecord.fullFileName != null && maxVersionRecord.fullCrc32 != null) {
                    whiteLists.add(
                        WhiteList.newBuilder()
                            .setVersion(maxVersionRecord.version!!)
                            .setUpdateType(WhiteListUpdateType.WLU_FULL)
                            .setSubType(subType)
                            .setUrl("$baseUrl/wl/file/${maxVersionRecord.fullFileName}")
                            .setCrc(maxVersionRecord.fullCrc32)
                            .build()
                    )
                }
            } else {
                // Для version > 0 обрабатываем diff и full файлы
                val sortedRecords = records.sortedBy { it.version }
                
                if (sortedRecords.size <= 3) {
                    // Если diff файлов <= 3, отдаем их последовательно
                    sortedRecords.forEach { record ->
                        if (record.diffFileName != null && record.diffCrc32 != null) {
                            whiteLists.add(
                                WhiteList.newBuilder()
                                    .setVersion(record.version!!)
                                    .setUpdateType(WhiteListUpdateType.WLU_DIFF)
                                    .setSubType(subType)
                                    .setUrl("$baseUrl/wl/file/${record.diffFileName}")
                                    .setCrc(record.diffCrc32)
                                    .build()
                            )
                        }
                    }
                } else {
                    // Если diff файлов > 3, отдаем 1 full последней версии
                    val lastRecord = sortedRecords.last()
                    if (lastRecord.fullFileName != null && lastRecord.fullCrc32 != null) {
                        whiteLists.add(
                            WhiteList.newBuilder()
                                .setVersion(lastRecord.version!!)
                                .setUpdateType(WhiteListUpdateType.WLU_FULL)
                                .setSubType(subType)
                                .setUrl("$baseUrl/wl/file/${lastRecord.fullFileName}")
                                .setCrc(lastRecord.fullCrc32)
                                .build()
                        )
                    }
                }
            }
        }
        
        return whiteLists
    }
    
    /**
     * Создает список обновлений для одного подтипа с учетом версии
     */
    fun createWhiteListUpdatesForSubType(
        whiteListRecords: List<AbtWhiteList>,
        baseUrl: String,
        requestedVersion: Long
    ): List<WhiteList> {
        val whiteLists = mutableListOf<WhiteList>()
        
        if (whiteListRecords.isEmpty()) {
            return whiteLists
        }
        
        // Все записи относятся к одному подтипу, определяем его
        val firstRecord = whiteListRecords.first()
        val subType = WhiteListTypeConverter.toWhiteListSubType(
            SubscriptionType.valueOf(firstRecord.type ?: "ABT_TICKET"),
            HashType.values()[firstRecord.hashType ?: 0]
        )
        
        if (requestedVersion == 0L) {
            // Если version = 0, отдаем только full файлы максимальной версии
            val maxVersionRecord = whiteListRecords.maxByOrNull { it.version ?: 0L }
            if (maxVersionRecord != null && maxVersionRecord.fullFileName != null && maxVersionRecord.fullCrc32 != null) {
                whiteLists.add(
                    WhiteList.newBuilder()
                        .setVersion(maxVersionRecord.version!!)
                        .setUpdateType(WhiteListUpdateType.WLU_FULL)
                        .setSubType(subType)
                        .setUrl("$baseUrl/wl/file/${maxVersionRecord.fullFileName}")
                        .setCrc(maxVersionRecord.fullCrc32)
                        .build()
                )
            }
        } else {
            // Для version > 0 обрабатываем diff и full файлы
            val sortedRecords = whiteListRecords.sortedBy { it.version }
            
            // Проверяем, есть ли diff файлы
            val hasDiffFiles = sortedRecords.any { it.diffFileName != null && it.diffCrc32 != null }
            
            if (hasDiffFiles && sortedRecords.size <= 3) {
                // Если есть diff файлы и их <= 3, отдаем их последовательно
                sortedRecords.forEach { record ->
                    if (record.diffFileName != null && record.diffCrc32 != null) {
                        whiteLists.add(
                            WhiteList.newBuilder()
                                .setVersion(record.version!!)
                                .setUpdateType(WhiteListUpdateType.WLU_DIFF)
                                .setSubType(subType)
                                .setUrl("$baseUrl/wl/file/${record.diffFileName}")
                                .setCrc(record.diffCrc32)
                                .build()
                        )
                    }
                }
            } else {
                // Если diff файлов нет или их > 3, отдаем 1 full последней версии
                val lastRecord = sortedRecords.last()
                if (lastRecord.fullFileName != null && lastRecord.fullCrc32 != null) {
                    whiteLists.add(
                        WhiteList.newBuilder()
                            .setVersion(lastRecord.version!!)
                            .setUpdateType(WhiteListUpdateType.WLU_FULL)
                            .setSubType(subType)
                            .setUrl("$baseUrl/wl/file/${lastRecord.fullFileName}")
                            .setCrc(lastRecord.fullCrc32)
                            .build()
                    )
                }
            }
        }
        
        return whiteLists
    }
    
    /**
     * Получает максимальную версию из списка записей
     */
    fun getMaxVersion(whiteListRecords: List<AbtWhiteList>): Long {
        return whiteListRecords.maxOfOrNull { it.version ?: 0L } ?: 0L
    }
    
    /**
     * Проверяет, есть ли обновления для указанной версии
     */
    fun hasUpdates(whiteListRecords: List<AbtWhiteList>, version: Long): Boolean {
        return whiteListRecords.any { (it.version ?: 0L) > version }
    }
}
