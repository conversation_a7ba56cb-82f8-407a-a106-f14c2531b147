package ru.sbertroika.tkp3.whitelist.gate.service.impl

import arrow.core.Either
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.common.white.list.WhiteListType
import ru.sbertroika.common.white.list.whiteListUpdate
import ru.sbertroika.tkp3.whitelist.gate.output.repository.AbtWhiteListRepository
import ru.sbertroika.tkp3.whitelist.gate.service.WhiteListService
import ru.sbertroika.tkp3.whitelist.gate.util.WhiteListTypeConverter
import ru.sbertroika.tkp3.whitelist.gate.util.WhiteListVersionManager
import ru.sbertroika.white.list.gate.v1.WhiteListUpdateRequest
import java.util.*

@Service
class WhiteListServiceImpl(
    @Value("\${base_url}")
    private val baseUrl: String,
    
    private val abtWhiteListRepository: AbtWhiteListRepository
) : WhiteListService {

    override suspend fun findWhiteListUpdate(request: WhiteListUpdateRequest): Either<Throwable, ru.sbertroika.common.white.list.WhiteListUpdate> = Either.catch {
        when (request.type) {
            WhiteListType.WL_ABT -> {
                val projectId = UUID.fromString(request.projectId)
                val whiteLists = mutableListOf<ru.sbertroika.common.white.list.WhiteList>()

                // Получаем все доступные подтипы для проекта
                val allAvailableRecords = abtWhiteListRepository.findMaxVersionsByProject(projectId)
                
                if (request.curListList.isEmpty()) {
                    // Если список пустой, возвращаем все доступные подтипы с максимальными версиями
                    allAvailableRecords.forEach { record ->
                        val subType = WhiteListTypeConverter.toWhiteListSubType(
                            ru.sbertroika.tkp3.whitelist.api.model.SubscriptionType.valueOf(record.type ?: "ABT_TICKET"),
                            ru.sbertroika.tkp3.whitelist.api.model.HashType.values()[record.hashType ?: 0]
                        )
                        
                        if (record.fullFileName != null && record.fullCrc32 != null) {
                            whiteLists.add(
                                ru.sbertroika.common.white.list.WhiteList.newBuilder()
                                    .setVersion(record.version!!)
                                    .setUpdateType(ru.sbertroika.common.white.list.WhiteListUpdateType.WLU_FULL)
                                    .setSubType(subType)
                                    .setUrl("$baseUrl/wl/file/${record.fullFileName}")
                                    .setCrc(record.fullCrc32)
                                    .build()
                            )
                        }
                    }
                } else {
                    // Создаем карту запрошенных версий по подтипам
                    val requestedVersions = request.curListList.associate { 
                        it.subType to it.version 
                    }
                    
                    // Для каждого доступного подтипа определяем, что нужно вернуть
                    allAvailableRecords.forEach { record ->
                        val subType = WhiteListTypeConverter.toWhiteListSubType(
                            ru.sbertroika.tkp3.whitelist.api.model.SubscriptionType.valueOf(record.type ?: "ABT_TICKET"),
                            ru.sbertroika.tkp3.whitelist.api.model.HashType.values()[record.hashType ?: 0]
                        )
                        
                        val requestedVersion = requestedVersions[subType] ?: 0L
                        
                        if (requestedVersion == 0L) {
                            // Если версия не запрошена или = 0, возвращаем максимальную версию
                            if (record.fullFileName != null && record.fullCrc32 != null) {
                                whiteLists.add(
                                    ru.sbertroika.common.white.list.WhiteList.newBuilder()
                                        .setVersion(record.version!!)
                                        .setUpdateType(ru.sbertroika.common.white.list.WhiteListUpdateType.WLU_FULL)
                                        .setSubType(subType)
                                        .setUrl("$baseUrl/wl/file/${record.fullFileName}")
                                        .setCrc(record.fullCrc32)
                                        .build()
                                )
                            }
                        } else {
                            // Если есть запрошенная версия, проверяем есть ли обновления
                            if (record.version!! > requestedVersion) {
                                // Ищем обновления после запрошенной версии
                                val updateRecords = abtWhiteListRepository.findLastByProjectTypeHashTypeAndVersion(
                                    projectId,
                                    record.type ?: "ABT_TICKET",
                                    record.hashType ?: 0,
                                    requestedVersion
                                )
                                
                                if (updateRecords.isNotEmpty()) {
                                    // Если есть обновления, обрабатываем их
                                    val subTypeWhiteLists = WhiteListVersionManager.createWhiteListUpdatesForSubType(
                                        updateRecords,
                                        baseUrl,
                                        requestedVersion
                                    )
                                    whiteLists.addAll(subTypeWhiteLists)
                                } else {
                                    // Если обновлений нет, но максимальная версия больше запрошенной,
                                    // возвращаем максимальную версию (full файл)
                                    if (record.fullFileName != null && record.fullCrc32 != null) {
                                        whiteLists.add(
                                            ru.sbertroika.common.white.list.WhiteList.newBuilder()
                                                .setVersion(record.version!!)
                                                .setUpdateType(ru.sbertroika.common.white.list.WhiteListUpdateType.WLU_FULL)
                                                .setSubType(subType)
                                                .setUrl("$baseUrl/wl/file/${record.fullFileName}")
                                                .setCrc(record.fullCrc32)
                                                .build()
                                        )
                                    }
                                }
                            }
                            // Если record.version <= requestedVersion, то обновлений нет, ничего не добавляем
                        }
                    }
                }
                
                // Возвращаем результат
                whiteListUpdate {
                    this.type = request.type
                    list.addAll(whiteLists)
                }
            }
            WhiteListType.UNRECOGNIZED -> whiteListUpdate {
                this.type = request.type
            }
        }
    }
}
