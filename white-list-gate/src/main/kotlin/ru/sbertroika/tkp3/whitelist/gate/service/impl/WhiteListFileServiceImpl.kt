package ru.sbertroika.tkp3.whitelist.gate.service.impl

import arrow.core.Either
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.whitelist.gate.service.WhiteListFileService
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.GetObjectResponse

@Service
class WhiteListFileServiceImpl(
    private val s3Client: S3Client,

    @Value("\${s3.bucket}")
    val bucket: String
) : WhiteListFileService {

    override suspend fun getFile(file: String): Either<Throwable, ResponseInputStream<GetObjectResponse>> = Either.catch {
        s3Client.getObject(GetObjectRequest.builder().bucket(bucket).key(file).build())
    }
}