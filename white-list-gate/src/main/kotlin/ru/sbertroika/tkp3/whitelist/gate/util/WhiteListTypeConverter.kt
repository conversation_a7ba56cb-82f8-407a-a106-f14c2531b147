package ru.sbertroika.tkp3.whitelist.gate.util

import ru.sbertroika.common.white.list.WhiteListSubType
import ru.sbertroika.tkp3.whitelist.api.model.HashType
import ru.sbertroika.tkp3.whitelist.api.model.SubscriptionType

/**
 * Утилиты для преобразования типов белых списков
 */
object WhiteListTypeConverter {
    
    /**
     * Преобразует SubscriptionType + HashType в WhiteListSubType
     */
    fun toWhiteListSubType(subscriptionType: SubscriptionType, hashType: HashType): WhiteListSubType {
        return when {
            // ABT_TICKET + UID
            subscriptionType == SubscriptionType.ABT_TICKET && hashType == HashType.UID -> 
                WhiteListSubType.WLH_MIFARE_SUBSCRIPTION
            
            // ABT_WALLET + UID
            subscriptionType == SubscriptionType.ABT_WALLET && hashType == HashType.UID -> 
                WhiteListSubType.WLH_MIFARE_WALLET
            
            // PROSTOR_TICKET/PROSTOR_WALLET + UID
            (subscriptionType == SubscriptionType.PROSTOR_TICKET || subscriptionType == SubscriptionType.PROSTOR_WALLET) && hashType == HashType.UID -> 
                WhiteListSubType.WLH_MIFARE_PROSTOR
            
            // ABT_TICKET + SHA256
            subscriptionType == SubscriptionType.ABT_TICKET && hashType == HashType.SHA256 -> 
                WhiteListSubType.WLH_PAN_SHA256_SUBSCRIPTION
            
            // ABT_WALLET + SHA256
            subscriptionType == SubscriptionType.ABT_WALLET && hashType == HashType.SHA256 -> 
                WhiteListSubType.WLH_PAN_SHA256_WALLET
            
            // ABT_TICKET + SHA1
            subscriptionType == SubscriptionType.ABT_TICKET && hashType == HashType.SHA1 -> 
                WhiteListSubType.WLH_PAN_SHA1_SUBSCRIPTION
            
            // ABT_WALLET + SHA1
            subscriptionType == SubscriptionType.ABT_WALLET && hashType == HashType.SHA1 -> 
                WhiteListSubType.WLH_PAN_SHA1_WALLET
            
            else -> {
                // fallback на MIFARE_SUBSCRIPTION для неизвестных комбинаций
                WhiteListSubType.WLH_MIFARE_SUBSCRIPTION
            }
        }
    }
    
    /**
     * Преобразует WhiteListSubType обратно в пару SubscriptionType + HashType
     */
    fun fromWhiteListSubType(whiteListSubType: WhiteListSubType): Pair<SubscriptionType, HashType> {
        return when (whiteListSubType) {
            WhiteListSubType.WLH_MIFARE_SUBSCRIPTION -> Pair(SubscriptionType.ABT_TICKET, HashType.UID)
            WhiteListSubType.WLH_MIFARE_WALLET -> Pair(SubscriptionType.ABT_WALLET, HashType.UID)
            WhiteListSubType.WLH_MIFARE_PROSTOR -> Pair(SubscriptionType.PROSTOR_TICKET, HashType.UID)
            WhiteListSubType.WLH_PAN_SHA256_SUBSCRIPTION -> Pair(SubscriptionType.ABT_TICKET, HashType.SHA256)
            WhiteListSubType.WLH_PAN_SHA256_WALLET -> Pair(SubscriptionType.ABT_WALLET, HashType.SHA256)
            WhiteListSubType.WLH_PAN_SHA1_SUBSCRIPTION -> Pair(SubscriptionType.ABT_TICKET, HashType.SHA1)
            WhiteListSubType.WLH_PAN_SHA1_WALLET -> Pair(SubscriptionType.ABT_WALLET, HashType.SHA1)
            else -> Pair(SubscriptionType.ABT_TICKET, HashType.UID) // fallback
        }
    }
    
    /**
     * Получает все возможные комбинации SubscriptionType + HashType
     */
    fun getAllSubscriptionTypeHashTypePairs(): List<Pair<SubscriptionType, HashType>> {
        return listOf(
            Pair(SubscriptionType.ABT_TICKET, HashType.UID),
            Pair(SubscriptionType.ABT_WALLET, HashType.UID),
            Pair(SubscriptionType.PROSTOR_TICKET, HashType.UID),
            Pair(SubscriptionType.PROSTOR_WALLET, HashType.UID),
            Pair(SubscriptionType.ABT_TICKET, HashType.SHA256),
            Pair(SubscriptionType.ABT_WALLET, HashType.SHA256),
            Pair(SubscriptionType.ABT_TICKET, HashType.SHA1),
            Pair(SubscriptionType.ABT_WALLET, HashType.SHA1)
        )
    }
} 