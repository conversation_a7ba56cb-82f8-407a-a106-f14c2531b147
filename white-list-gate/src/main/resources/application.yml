spring:
  application:
    name: white-list-gate
  main:
    allow-bean-definition-overriding: true

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://localhost:5432/white_list_db}

input_log:
  enabled: ${INPUT_LOG_ENABLED:true}

grpc:
  port: 5000
server:
  port: 8080


s3:
  url: ${S3_URL:localhost:8080}
  access_key_id: ${S3_ACCESS_KEY_ID:fksdjfksd}
  secret_access_key: ${S3_SECRET_ACCESS_KEY:fsdfklsd}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-wl}

base_url: ${BASE_URL:http://localhost:8080}

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization

management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        liveness:
          include: '*'
        readiness:
          include: '*'