package ru.sbertroika.tkp3.whitelist.gate.input

import io.grpc.*
import io.netty.handler.ssl.util.InsecureTrustManagerFactory
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import ru.sbertroika.common.white.list.WhiteListSubType
import ru.sbertroika.common.white.list.WhiteListType
import ru.sbertroika.common.white.list.whiteList
import ru.sbertroika.white.list.gate.v1.WhiteListGateServiceGrpcKt
import ru.sbertroika.white.list.gate.v1.whiteListUpdateRequest

class WhiteListGateServiceGrpcTest {

    companion object {
        private const val server = "localhost"
        private const val port = 5055
        private const val isTls = false
    }

    @Test
    fun getWhiteListUpdateTest(): Unit = runBlocking {
        val response = client().getWhiteListUpdate(
            whiteListUpdateRequest {
                projectId = "a223116c-49b4-43eb-8a47-c76a68a4225d"
                type = WhiteListType.WL_ABT
                // Добавляем пустой список для тестирования
                curList += emptyList()
            }
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getWhiteListUpdateTest2(): Unit = runBlocking {
        val response = client().getWhiteListUpdate(
            whiteListUpdateRequest {
                projectId = "a223116c-49b4-43eb-8a47-c76a68a4225d"
                type = WhiteListType.WL_ABT
                // Добавляем пустой список для тестирования
                curList += listOf(
                    whiteList {
                        subType = WhiteListSubType.WLH_MIFARE_WALLET
                        version = 10
                    }
                )
            }
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getWhiteListUpdateTest3(): Unit = runBlocking {
        val response = client().getWhiteListUpdate(
            whiteListUpdateRequest {
                projectId = "a223116c-49b4-43eb-8a47-c76a68a4225d"
                type = WhiteListType.WL_ABT
                // Добавляем пустой список для тестирования
                curList += listOf(
                    whiteList {
                        subType = WhiteListSubType.WLH_MIFARE_WALLET
                        version = 1755673505
                    }
                )
            }
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    private fun client(): WhiteListGateServiceGrpcKt.WhiteListGateServiceCoroutineStub {
        return if (isTls) {
            val credentials: ChannelCredentials = TlsChannelCredentials.newBuilder() //You can use your own certificate here .trustManager(new File("cert.pem"))
                .trustManager(InsecureTrustManagerFactory.INSTANCE.trustManagers[0])
                .build()
            val channel: ManagedChannel = Grpc.newChannelBuilderForAddress(
                server,
                port, credentials
            ).build()
            return WhiteListGateServiceGrpcKt.WhiteListGateServiceCoroutineStub(channel)
        } else {
            val channel = ManagedChannelBuilder.forTarget("${server}:${port}")
                .usePlaintext()
                .build()
            WhiteListGateServiceGrpcKt.WhiteListGateServiceCoroutineStub(channel)
        }
    }

}
