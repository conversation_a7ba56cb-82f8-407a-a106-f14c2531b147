include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/java-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates

# ========================
# white-list-gate: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
white_list_gate_build_develop:
  stage: build
  needs: [develop_build_test_publish]
  variables:
    SERVICE_NAME: "white-list-gate"
    TAG: "$CI_COMMIT_SHORT_SHA"
  extends:
    - .docker_gradle_build_and_push
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - white-list-gate/**
        - ./*

white_list_gate_helm_kubeval_testing_develop:
  stage: test
  needs:
    - job: white_list_gate_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    SERVICE_NAME: "white-list-gate"
  extends:
    - .validate_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - white-list-gate/**
        - charts/white-list-gate/**

white_list_gate_deploy_chart_develop:
  stage: deploy
  needs:
    - white_list_gate_helm_kubeval_testing_develop
    - job: white_list_gate_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    STAGE: "dev"
    SERVICE_NAME: "white-list-gate"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - white-list-gate/**
        - charts/white-list-gate/**

# --- TAG ---
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - white-list-gate/**

white_list_gate_build_tag:
  stage: build
  variables:
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "white-list-gate"
  extends:
    - .docker_gradle_build_and_push
  <<: *tag_rules

white_list_gate_helm_kubeval_testing_tag:
  stage: test
  needs:
    - white_list_gate_build_tag
  variables:
    SERVICE_NAME: "white-list-gate"
  extends:
    - .validate_helm_template
  <<: *tag_rules

white_list_gate_deploy_chart_tag:
  stage: deploy
  needs:
    - white_list_gate_helm_kubeval_testing_tag
  variables:
    STAGE: "dev"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "white-list-gate"
  extends:
    - .deploy_helm_template
  <<: *tag_rules

white_list_gate_deploy_prod:
  stage: deploy
  needs:
    - white_list_gate_deploy_chart_tag
  variables:
    STAGE: "prod"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "white-list-gate"
    NAMESPACE: "$KUBE_NAMESPACE-prod"
    KUBECONFIG_FILE: $KUBECONFIG_CCE_PROD
  extends:
    - .deploy_helm_template
  when: manual
  <<: *tag_rules
