package ru.sbertroika.whitelist

class Whitelist {
    /**
     * Инициализация библиотеки
     *
     * @param pathToFile
     * путь к файлу для сохранения логов
     *
     * @param hashKey
     * ключ шифрования
     * Если ключ не задан, то проверка по стоп-листам, где он используется, не выполняется
     *
     * @param isDebug
     * включение/отключение отладочного логирования библиотеки
     *
     * @param sizeBackupLog
     * максимальный размер файла логов в МБ. Если не задан, то равен 4 МБ
     *
     * @return true - в случаи успешной инициализации
     */
    external fun initLib(
        pathToFile: String,
        hashKey: String = "",
        isDebug: Boolean = false,
        sizeBackupLog: Short = 0
    ): Boolean

    external fun isWhiteList(typeWhiteList: Int, pathToFile: String, pan_or_uid: String): Array<WhiteListHashItem>

    /**
     * Добавить запись в белый список
     *
     * @param pathToFile
     * путь к файлу белого списка
     *
     * @param typeWhiteList
     * тип белого списка
     *
     * @param panOrUid
     * PAN или UID карты
     *
     * @param abonementId
     * ID абонемента
     *
     * @param writeOffsId
     * ID списания
     *
     * @param expirationDateTime
     * дата истечения
     *
     * @param creationDateTime
     * дата создания
     *
     * @param balance
     * баланс
     *
     * @param isHash
     * флаг хеширования (по умолчанию false)
     *
     * @return true - если запись добавлена успешно, иначе - false
     */
    external fun insertWhiteListItem(
        pathToFile: String,
        typeWhiteList: Int,
        panOrUid: String,
        abonementId: Long,
        writeOffsId: Int,
        expirationDateTime: Long,
        creationDateTime: Long,
        balance: String,
        isHash: Boolean = false
    ): Boolean

    /**
     * Обновить запись в белом списке
     *
     * @param typeWhiteList
     * тип белого списка
     *
     * @param pathToFile
     * путь к файлу белого списка
     *
     * @param panOrUid
     * PAN или UID карты
     *
     * @param abonementId
     * ID абонемента
     *
     * @param writeOffsId
     * ID списания
     *
     * @param expirationDateTime
     * дата истечения
     *
     * @param creationDateTime
     * дата создания
     *
     * @param balance
     * баланс
     *
     * @param isHash
     * флаг хеширования (по умолчанию false)
     *
     * @return true - если запись обновлена успешно, иначе - false
     */
    external fun updateWhiteListItem(
        typeWhiteList: Int,
        pathToFile: String,
        panOrUid: String,
        abonementId: Long,
        writeOffsId: Int,
        expirationDateTime: Long,
        creationDateTime: Long,
        balance: String,
        isHash: Boolean = false
    ): Boolean

    /**
     * Удалить запись из белого списка
     *
     * @param typeWhiteList
     * тип белого списка
     *
     * @param pathToFile
     * путь к файлу белого списка
     *
     * @param panOrUid
     * PAN или UID карты
     *
     * @param abonementId
     * ID абонемента
     *
     * @param isHash
     * флаг хеширования (по умолчанию false)
     *
     * @return true - если запись удалена успешно, иначе - false
     */
    external fun deleteWhiteListItem(
        typeWhiteList: Int,
        pathToFile: String,
        panOrUid: String,
        abonementId: Long,
        isHash: Boolean = false
    ): Boolean

    /**
     * Применить diff-файл белых списков
     *
     * @param pathToSrcFile
     * путь к файлу, к которому надо применить diff
     * @param pathToDiffFile
     * путь к diff-файлу
     *
     * @return true - если diff-файл применился без ошибок, иначе - false
     */
    external fun acceptDiff(pathToSrcFile: String, pathToDiffFile: String): Boolean

    /**
     * Показать содержимое белого списка
     *
     * @param typeWhiteList
     * тип белого списка
     *
     * @param filePath
     * путь к файлу белого списка
     *
     * Выводит отладочную информацию о содержимом белого списка в лог
     */
    external fun showWhiteList(typeWhiteList: Int, filePath: String)

//     /**
//      * Нативная реализация генерации diff файла
//      */
//     private external fun nativeGenerateDiff(pathToOldFile: String,
//                                             pathToNewFile: String,
//                                             pathToDiffFile: String,
//                                             elementSize: Int)

    companion object {

        init {
            System.loadLibrary("whitelist-jni")
        }

        // Константы типов белых списков (для обратной совместимости)
        @Deprecated("Используйте WhiteListType enum вместо констант", ReplaceWith("WhiteListType.MIFARE_WALLET_ENTRY.value"))
        const val MIFARE_WALLET_ENTRY = 0x05          // MIFARE Wallet Entry
        
        @Deprecated("Используйте WhiteListType enum вместо констант", ReplaceWith("WhiteListType.MIFARE_SUBSCRIPTION_ENTRY.value"))
        const val MIFARE_SUBSCRIPTION_ENTRY = 0x0C    // MIFARE Subscription Entry
        
        @Deprecated("Используйте WhiteListType enum вместо констант", ReplaceWith("WhiteListType.MIFARE_PROSTOR.value"))
        const val MIFARE_PROSTOR = 0x14                // MIFARE PROSTOR
        
        @Deprecated("Используйте WhiteListType enum вместо констант", ReplaceWith("WhiteListType.PAN_SHA256_SUBSCRIPTION.value"))
        const val PAN_SHA256_SUBSCRIPTION = 0x01       // EMV SHA256 Subscription
        
        @Deprecated("Используйте WhiteListType enum вместо констант", ReplaceWith("WhiteListType.PAN_SHA256_WALLET_ENTRY.value"))
        const val PAN_SHA256_WALLET_ENTRY = 0x04       // EMV SHA256 Wallet Entry
        
        @Deprecated("Используйте WhiteListType enum вместо констант", ReplaceWith("WhiteListType.PAN_SHA1_WALLET_ENTRY.value"))
        const val PAN_SHA1_WALLET_ENTRY = 0x15         // EMV SHA1 Wallet Entry
        
        @Deprecated("Используйте WhiteListType enum вместо констант", ReplaceWith("WhiteListType.PAN_SHA1_SUBSCRIPTION.value"))
        const val PAN_SHA1_SUBSCRIPTION = 0x16         // EMV SHA1 Subscription

//         private fun isFileExist(filename: String): Boolean {
//             return File(filename).exists()
//         }
//
//         /**
//          * Генерация diff файла
//          *
//          * @param pathToOldFile
//          * путь к файлу с исходными данными
//          *
//          * @param pathToNewFile
//          * путь к файлу с новыми данными
//          *
//          * @param pathToDiffFile
//          * путь к файлу, куда будет сохранён результат выполнения
//          *
//          * @param elementSize
//          * размер одного элемента данных стоп-листа
//          * @see StoplistElementSize
//          *
//          * @return [StoplistResult.SUCCESS] - в случае успеха, иначе код ошибки
//          * @see StoplistResult
//          */
//         fun generateDiff(pathToOldFile: String,
//                          pathToNewFile: String,
//                          pathToDiffFile: String,
//                          elementSize: StoplistElementSize): StoplistResult  {
//
//             if (pathToOldFile.isBlank() || pathToNewFile.isBlank() || pathToDiffFile.isBlank()) {
//                 return StoplistResult.E_FILENAME_BLANK
//             }
//
//             if (!isFileExist(pathToOldFile) || !isFileExist(pathToNewFile)) {
//                 return StoplistResult.E_FILENAME_NO_EXIST
//             }
//
//             Stoplist().nativeGenerateDiff(pathToOldFile, pathToNewFile, pathToDiffFile, elementSize.value)
//
//             return StoplistResult.SUCCESS
//         }
    }
}
