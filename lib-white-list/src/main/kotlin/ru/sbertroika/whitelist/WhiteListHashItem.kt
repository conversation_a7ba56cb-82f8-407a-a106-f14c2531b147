package ru.sbertroika.whitelist

class WhiteListHashItem {

    val typeItem: Int
    val abonementId: Long
    val writeOffsId: Int
    val expirationDateTime: Long
    val creationDateTime: Long
    val balance: String
    val pan_or_uid: String

    constructor(
        typeItem: Int,
        pan_or_uid: String,
        abonementId: Long,
        writeOffsId: Int,
        expirationDateTime: Long,
        creationDateTime: Long,
        balance: String
    ) {
       this.typeItem           = typeItem
       this.pan_or_uid         = pan_or_uid
       this.abonementId        = abonementId
       this.writeOffsId        = writeOffsId
       this.expirationDateTime = expirationDateTime
       this.creationDateTime   = creationDateTime
       this.balance            = balance
    }
}
