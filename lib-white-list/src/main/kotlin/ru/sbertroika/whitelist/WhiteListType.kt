package ru.sbertroika.whitelist

/**
 * Enum класс для типов белых списков
 * 
 * Содержит все поддерживаемые типы белых списков с их числовыми значениями
 */
enum class WhiteListType(val value: Int) {
    /**
     * MIFARE Wallet Entry
     * Тип белого списка для MIFARE кошельковых карт
     */
    MIFARE_WALLET_ENTRY(0x05),
    
    /**
     * MIFARE Subscription Entry  
     * Тип белого списка для MIFARE подписочных карт
     */
    MIFARE_SUBSCRIPTION_ENTRY(0x0C),
    
    /**
     * MIFARE PROSTOR
     * Тип белого списка для MIFARE PROSTOR карт
     */
    MIFARE_PROSTOR(0x14),
    
    /**
     * EMV SHA256 Subscription
     * Тип белого списка для EMV подписочных карт с хешированием SHA256
     */
    PAN_SHA256_SUBSCRIPTION(0x01),
    
    /**
     * EMV SHA256 Wallet Entry
     * Тип белого списка для EMV кошельковых карт с хешированием SHA256
     */
    PAN_SHA256_WALLET_ENTRY(0x04),
    
    /**
     * EMV SHA1 Wallet Entry
     * Тип белого списка для EMV кошельковых карт с хешированием SHA1
     */
    PAN_SHA1_WALLET_ENTRY(0x15),
    
    /**
     * EMV SHA1 Subscription
     * Тип белого списка для EMV подписочных карт с хешированием SHA1
     */
    PAN_SHA1_SUBSCRIPTION(0x16);
    
    companion object {

        /**
         * Получить тип белого списка по числовому значению
         * 
         * @param value числовое значение типа
         * @return соответствующий WhiteListType или null если не найден
         */
        fun fromValue(value: Int): WhiteListType? {
            return values().find { it.value == value }
        }
        
        /**
         * Получить все типы MIFARE карт
         * 
         * @return список типов MIFARE карт
         */
        fun getMifareTypes(): List<WhiteListType> {
            return listOf(
                MIFARE_WALLET_ENTRY,
                MIFARE_SUBSCRIPTION_ENTRY,
                MIFARE_PROSTOR
            )
        }
        
        /**
         * Получить все типы EMV карт
         * 
         * @return список типов EMV карт
         */
        fun getEmvTypes(): List<WhiteListType> {
            return listOf(
                PAN_SHA256_SUBSCRIPTION,
                PAN_SHA256_WALLET_ENTRY,
                PAN_SHA1_WALLET_ENTRY,
                PAN_SHA1_SUBSCRIPTION
            )
        }
        
        /**
         * Получить все типы SHA256 карт
         * 
         * @return список типов SHA256 карт
         */
        fun getSha256Types(): List<WhiteListType> {
            return listOf(
                PAN_SHA256_SUBSCRIPTION,
                PAN_SHA256_WALLET_ENTRY
            )
        }
        
        /**
         * Получить все типы SHA1 карт
         * 
         * @return список типов SHA1 карт
         */
        fun getSha1Types(): List<WhiteListType> {
            return listOf(
                PAN_SHA1_WALLET_ENTRY,
                PAN_SHA1_SUBSCRIPTION
            )
        }
        
        /**
         * Проверить, является ли тип MIFARE картой
         * 
         * @param type тип для проверки
         * @return true если это MIFARE тип, false иначе
         */
        fun isMifareType(type: WhiteListType): Boolean {
            return getMifareTypes().contains(type)
        }
        
        /**
         * Проверить, является ли тип EMV картой
         * 
         * @param type тип для проверки
         * @return true если это EMV тип, false иначе
         */
        fun isEmvType(type: WhiteListType): Boolean {
            return getEmvTypes().contains(type)
        }
        
        /**
         * Проверить, требует ли тип хеширования
         * 
         * @param type тип для проверки
         * @return true если тип требует хеширования (EMV), false иначе (MIFARE)
         */
        fun requiresHashing(type: WhiteListType): Boolean {
            return isEmvType(type)
        }
        
        /**
         * Получить размер блока для типа белого списка
         * 
         * @param type тип белого списка
         * @return размер блока в байтах
         */
        fun getBlockSize(type: WhiteListType): Int {
            return when (type) {
                MIFARE_PROSTOR -> 36
                MIFARE_WALLET_ENTRY -> 39
                MIFARE_SUBSCRIPTION_ENTRY -> 96
                PAN_SHA256_SUBSCRIPTION -> 128
                PAN_SHA256_WALLET_ENTRY -> 71
                PAN_SHA1_SUBSCRIPTION -> 116
                PAN_SHA1_WALLET_ENTRY -> 59
            }
        }
        
        /**
         * Получить описание типа белого списка
         * 
         * @param type тип белого списка
         * @return человекочитаемое описание типа
         */
        fun getDescription(type: WhiteListType): String {
            return when (type) {
                MIFARE_WALLET_ENTRY -> "MIFARE Wallet Entry (кошельковая карта)"
                MIFARE_SUBSCRIPTION_ENTRY -> "MIFARE Subscription Entry (подписочная карта)"
                MIFARE_PROSTOR -> "MIFARE PROSTOR"
                PAN_SHA256_SUBSCRIPTION -> "EMV SHA256 Subscription (подписка с хешированием SHA256)"
                PAN_SHA256_WALLET_ENTRY -> "EMV SHA256 Wallet Entry (кошелек с хешированием SHA256)"
                PAN_SHA1_WALLET_ENTRY -> "EMV SHA1 Wallet Entry (кошелек с хешированием SHA1)"
                PAN_SHA1_SUBSCRIPTION -> "EMV SHA1 Subscription (подписка с хешированием SHA1)"
            }
        }
    }
}
