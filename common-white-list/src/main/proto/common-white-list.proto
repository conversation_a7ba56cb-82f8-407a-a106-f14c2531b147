syntax = "proto3";

package ru.sbertroika.common.white.list;

option java_multiple_files = true;
option java_package = "ru.sbertroika.common.white.list";

enum WhiteListUpdateType {
  WLU_FULL = 0;
  WLU_DIFF = 1;
}

enum WhiteListType {
  WL_ABT = 0;
}

enum WhiteListSubType {
  WLH_MIFARE_WALLET = 0;
  WLH_MIFARE_SUBSCRIPTION = 1;
  WLH_MIFARE_PROSTOR = 2;
  WLH_PAN_SHA256_SUBSCRIPTION = 3;
  WLH_PAN_SHA256_WALLET = 4;
  WLH_PAN_SHA1_WALLET = 5;
  WLH_PAN_SHA1_SUBSCRIPTION = 6;
}

message WhiteList {
  uint64 version = 1;                        // Новая версия белого списка
  WhiteListUpdateType updateType = 2;        // Тип файла
  WhiteListSubType subType = 3;              // Подтип, может не быть
  string url = 4;                            // Ссылка на скачивание белого списка
  string crc = 5;                            // CRC32 для контроля целостности файла белого списка
}

message WhiteListUpdate {
  WhiteListType type = 1;                   // Тип белого списка
  repeated WhiteList list = 2;              // Список обновлений
}
