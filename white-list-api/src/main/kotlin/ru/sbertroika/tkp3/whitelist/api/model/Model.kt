package ru.sbertroika.tkp3.whitelist.api.model

import com.fasterxml.jackson.annotation.JsonFormat
import java.util.*

/**
 * Белый список
 */
data class AbtWhiteList(

    /**
     * Идентификатор проекта
     */
    val project: UUID,

    /**
     * Версия белого списка
     */
    val version: Long,

    /**
     * Записи белого списка
     */
    val records: List<AbtWhiteListOperationRecord> = emptyList(),

    /**
     * Требуется ли полный сброс перед применением операций
     */
    val isReset: Boolean = false,

    /**
     * Путь к файлу в S3 (для больших файлов)
     */
    val path: String? = null,

    /**
     * Имя файла с данными в S3 (для больших файлов)
     */
    val srcFile: String? = null
)

/**
 * Запись белого списка
 */
data class AbtWhiteListOperationRecord(

    /**
     * Идентификатор записи белого списка
     */
    val hash: String,

    /**
     * Признак Payment Account Reference
     */
    val hashType: HashType,

    /**
     * Идентификатор подписки (абонемента/кошелька)
     */
    val abonementId: String,

    /**
     * Идентификатор шаблона подписки (абонемента/кошелька)
     * WriteOffsId
     */
    val templateId: String,

    /**
     * Данные подписки (абонемента/кошелька)
     * Может не быть если абонемент не содержит счетчиков или другой информации
     */
    val bitmap: String = "",

    /**
     * Размер данных
     */
    val bitmapSize: Int = 0,

    /**
     * Дата начала срока действия абонемента
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val cDate: java.time.ZonedDateTime? = null,

    /**
     * Дата окончания срока действия абонемента
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val expDate: java.time.ZonedDateTime? = null,

    /**
     * Тип операции над записью белого списка
     */
    val operation: OperationType,

    /**
     * Тип источника
     */
    val type: SubscriptionType
)

enum class HashType {
    UID, SHA256, SHA1, HMAC_SHA1, HMAC_SHA256
}

enum class SubscriptionType {
    ABT_TICKET, ABT_WALLET, PROSTOR_TICKET, PROSTOR_WALLET
}

enum class OperationType {

    /**
     * Добавление записи
     */
    APPEND,

    /**
     * Изменение существующей записи
     */
    CHANGE,

    /**
     * Удаление существующей записи
     */
    DELETE
} 