syntax = "proto3";

package ru.sbertroika.white.list.gate.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common.proto";
import "common-white-list.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.white.list.gate.v1";

service WhiteListGateService {
  // Запрос обновления стоп-листа
  rpc getWhiteListUpdate(WhiteListUpdateRequest) returns (WhiteListUpdateResponse);
}

message WhiteListUpdateRequest {
  string projectId = 1;                                 // Идентификатор проекта
  common.white.list.WhiteListType type = 2;             // Тип стоп-листа
  repeated common.white.list.WhiteList curList = 3;     // Список текущих стоп-листов
}

message WhiteListUpdateResponse {
  oneof response {
    common.v1.OperationError error = 1;
    common.white.list.WhiteListUpdate result = 2;
  }
}