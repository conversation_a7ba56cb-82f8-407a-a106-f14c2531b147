package ru.sbertroika.white.list.model.db

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("abt_white_list_record")
data class AbtWhiteListRecord(
    @Id @Column("id")
    var id: UUID? = null,

    @Column("project_id")
    var projectId: UUID? = null,

    @Column("created_at")
    var createdAt: Timestamp? = null,

    @Column("abonement_id")
    var abonementId: String? = null,

    @Column("template_id")
    var templateId: String? = null,

    @Column("bitmap")
    var bitmap: String? = null,

    @Column("bitmap_size")
    var bitmapSize: Int? = null,

    @Column("uid")
    var uid: String? = null,

    @Column("hash")
    var hash: String? = null,

    @Column("hash_type")
    var hashType: Int? = null,

    @Column("version")
    var version: Long? = null,

    @Column("type")
    var type: String? = null,

    @Column("c_date")
    var cDate: Timestamp? = null,

    @Column("exp_date")
    var expDate: Timestamp? = null
) 