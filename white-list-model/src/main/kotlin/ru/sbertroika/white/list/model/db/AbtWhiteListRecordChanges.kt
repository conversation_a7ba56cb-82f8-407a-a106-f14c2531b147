package ru.sbertroika.white.list.model.db

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("abt_white_list_record_changes")
data class AbtWhiteListRecordChanges(
    @Id @Column("id")
    var id: UUID? = null,

    @Column("project_id")
    var projectId: UUID? = null,

    @Column("hash_type")
    var hashType: Int? = null,

    @Column("hash")
    var hash: String? = null,

    @Column("type")
    var type: String? = null,

    @Column("version")
    var version: Long? = null,

    @Column("record_id")
    var recordId: UUID? = null,

    @Column("operation")
    var operation: String? = null,

    @Column("abonement_id")
    var abonementId: String? = null,

    @Column("created_at")
    var createdAt: Timestamp? = null
) 