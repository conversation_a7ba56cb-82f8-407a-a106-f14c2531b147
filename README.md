# White List Domain

Домен для работы с белыми списками ABT (абонементов/кошельков).

## Описание

White List Domain - это комплексное решение для управления белыми списками абонементов и кошельков. Система поддерживает различные типы хэшей (UID, SHA256, SHA1, HMAC_SHA1, HMAC_SHA256) и типы подписок (ABT_TICKET, ABT_WALLET, PROSTOR_TICKET, PROSTOR_WALLET).

## Архитектура

Проект состоит из следующих модулей:

- **white-list-model** - модели данных для БД и API
- **white-list-api** - публичное API для работы с белыми списками
- **white-list-gate** - шлюз для терминалов
- **white-list-processing** - основной микросервис обработки
- **white-list-migrations** - миграции базы данных
- **common-white-list** - общие утилиты
- **lib-white-list** - библиотека для формирования файлов белых списков

## Основные возможности

### 1. Обработка операций с белыми списками

- **APPEND** - добавление новой записи
- **CHANGE** - изменение существующей записи  
- **DELETE** - удаление записи

### 2. Типы хэшей

- **UID** - уникальный идентификатор карты
- **SHA256** - SHA256 хэш PAN
- **SHA1** - SHA1 хэш PAN
- **HMAC_SHA1** - HMAC-SHA1 хэш
- **HMAC_SHA256** - HMAC-SHA256 хэш

### 3. Типы подписок

- **ABT_TICKET** - абонемент ABT
- **ABT_WALLET** - кошелек ABT
- **PROSTOR_TICKET** - абонемент Простор
- **PROSTOR_WALLET** - кошелек Простор

### 4. Формирование файлов

Система автоматически формирует файлы белых списков с использованием библиотеки `lib-white-list`:

- **Полные файлы** - содержат все записи для определенного типа хэша
- **Diff файлы** - содержат изменения относительно предыдущей версии

## Логика обработки

### Обработка операций

1. **APPEND операция**:
   - Проверяется существование записи с `projectId + hashType + hash`
   - Если запись существует → обновляется (CHANGE)
   - Если записи нет → создается новая (APPEND)

2. **CHANGE операция**:
   - Аналогично APPEND - проверяется существование и обновляется

3. **DELETE операция**:
   - Запись удаляется из БД
   - В таблицу изменений записывается операция DELETE

### Формирование файлов

1. **Проверка предыдущей версии**:
   - Если есть → скачивается из S3 и применяются изменения
   - Если нет → создается новый файл

2. **Применение изменений**:
   - **APPEND** → `insertWhiteListItem()`
   - **CHANGE** → `updateWhiteListItem()`
   - **DELETE** → `deleteWhiteListItem()`

3. **Особенности для новых файлов**:
   - Операции DELETE не выполняются
   - Операции CHANGE заменяются на APPEND

4. **Очистка после формирования**:
   - После успешного формирования файла все записи из `abt_white_list_record_changes` удаляются
   - Это предотвращает повторную обработку изменений при следующих сообщениях

5. **Сохранение метаданных**:
   - После формирования файла метаданные сохраняются в таблицу `abt_white_list`
   - Включает информацию о файле, CRC32, версии и типе хэша
   - При создании diff файла метаданные обновляются

### Маппинг типов

| SubscriptionType | HashType | WhiteListType | isHash |
|------------------|----------|---------------|---------|
| ABT_TICKET | UID | MIFARE_SUBSCRIPTION_ENTRY | false |
| ABT_WALLET | UID | MIFARE_WALLET_ENTRY | false |
| PROSTOR_TICKET/PROSTOR_WALLET | UID | MIFARE_PROSTOR | false |
| ABT_TICKET | SHA256 | PAN_SHA256_SUBSCRIPTION | true |
| ABT_WALLET | SHA256 | PAN_SHA256_WALLET_ENTRY | false |
| ABT_TICKET | SHA1 | PAN_SHA1_WALLET_ENTRY | false |
| ABT_WALLET | SHA1 | PAN_SHA1_SUBSCRIPTION | false |

## Структура базы данных

### Основные таблицы

1. **abt_white_list** - метаданные белых списков
2. **abt_white_list_record** - записи белых списков
3. **abt_white_list_record_changes** - изменения для формирования файлов

### Ключевые поля

- `project_id` - идентификатор проекта
- `hash_type` - тип хэша
- `hash` - хэш записи
- `version` - версия
- `type` - тип подписки
- `c_date` - дата начала действия
- `exp_date` - дата окончания действия

### Метаданные белых списков

- `project_id` - идентификатор проекта
- `hash_type` - тип хэша (0=UID, 1=SHA256, 2=SHA1, 3=HMAC_SHA1, 4=HMAC_SHA256)
- `version` - версия белого списка
- `type` - тип подписки (ABT_TICKET, ABT_WALLET, PROSTOR_TICKET, PROSTOR_WALLET)
- `full_filename` - имя файла полного списка
- `full_crc32` - CRC32 полного файла
- `diff_filename` - имя файла diff
- `diff_crc32` - CRC32 diff файла

## Реализованные компоненты

### 1. Сервисы

- **DBServiceImpl** - полная реализация работы с БД
- **KafkaServiceImpl** - реализация отправки сообщений в Kafka
- **WhiteListFileServiceImpl** - реализация формирования файлов
- **WhiteListLibraryService** - интеграция с библиотекой lib-white-list

### 2. Repository

- **AbtWhiteListRepository** - работа с метаданными
- **AbtWhiteListRecordRepository** - работа с записями
- **AbtWhiteListRecordChangesRepository** - работа с изменениями

### 3. Утилиты

- **FileUtils** - работа с файлами (создание, копирование, CRC32)
- **S3ServiceImpl** - работа с S3 хранилищем
- **JacksonMapper** - JSON сериализация/десериализация

### 4. Обработка ошибок

- Graceful fallback для отсутствующих файлов
- Логирование всех операций
- Обработка исключений с созданием пустых файлов
- Автоматическая очистка временных файлов
- Автоматическая очистка записей изменений после формирования файлов
- Автоматическое сохранение метаданных о версиях белых списков

### 5. Поиск и фильтрация

- Поиск по типу подписки (`ABT_TICKET`, `ABT_WALLET`, `PROSTOR_TICKET`, `PROSTOR_WALLET`)
- Поиск по проекту и типу подписки
- Поиск по типу подписки и версии
- Автоматическое определение типа подписки на основе записей изменений

## Настройка и запуск

### Требования

- Java 17+
- Gradle 8.5+
- PostgreSQL 15+
- Kafka 7.4+
- MinIO/S3

### Локальная разработка

```bash
# Запуск инфраструктуры
make docker-compose-up

# Сборка проекта
make build

# Запуск микросервиса
make run
```

### Переменные окружения

```bash
# База данных
DB_USERNAME=postgres
DB_PASSWORD=password

# Kafka
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_ABT_WHITE_LIST_TOPIC=PRO.WL.ABT
KAFKA_ABT_WHITE_LIST_JOURNAL_TOPIC=WL.JOURNAL.OUT

# S3
S3_BUCKET=white-list-bucket
S3_REGION=eu-central-1
```

## API

### Входящие сообщения

```json
{
  "project": "uuid",
  "version": 1,
  "records": [
    {
      "hash": "hash_value",
      "hashType": "UID",
      "abonementId": "abonement_id",
      "templateId": "template_id",
      "bitmap": "bitmap_data",
      "bitmapSize": 0,
      "cDate": "2024-01-01T00:00:00Z",
      "expDate": "2024-12-31T23:59:59Z",
      "operation": "APPEND",
      "type": "ABT_TICKET"
    }
  ],
  "isReset": false,
  "path": "s3_path",
  "srcFile": "file_name"
}
```

## Разработка

### Добавление новых типов

1. Добавить новый тип в `HashType` или `SubscriptionType`
2. Обновить маппинг в `WhiteListLibraryService.getWhiteListType()`
3. Добавить тесты

### Миграции БД

```bash
# Создание новой миграции
./gradlew :white-list-migrations:flywayMigrate
```

### Работа с файлами

Система использует `FileUtils` для безопасной работы с файлами:

```kotlin
// Создание директории
FileUtils.ensureDirectoryExists("/tmp/white-lists")

// Копирование файла
FileUtils.copyFile(source, destination)

// Вычисление CRC32
val crc32 = FileUtils.calculateCrc32(filePath)

// Очистка временных файлов
FileUtils.cleanupTempFiles("/tmp", 24) // удалить файлы старше 24 часов
```

## Мониторинг

- **Health Check**: `/actuator/health`
- **Metrics**: `/actuator/metrics`
- **Info**: `/actuator/info`

## Логирование

Логи доступны в консоли с уровнем DEBUG для пакета `ru.sbertroika.tkp3.whitelist`.

### Уровни логирования

- **DEBUG** - детальная информация о операциях
- **INFO** - основные события и операции
- **WARN** - предупреждения (например, отсутствующие файлы)
- **ERROR** - ошибки с детальной информацией

## Производительность

- Асинхронная обработка через корутины
- Пакетная обработка записей
- Кэширование метаданных
- Автоматическая очистка временных файлов
- Graceful fallback для критических операций 