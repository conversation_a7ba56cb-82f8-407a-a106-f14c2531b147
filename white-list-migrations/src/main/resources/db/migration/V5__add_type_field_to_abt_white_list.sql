-- Добавление поля type в таблицу abt_white_list
ALTER TABLE abt_white_list ADD COLUMN IF NOT EXISTS type VARCHAR(100);

ALTER TABLE abt_white_list_record_changes ADD COLUMN IF NOT EXISTS type VARCHAR(100);

-- Добавление комментария к новому полю
COMMENT ON COLUMN abt_white_list.type IS 'Тип подписки (ABT_TICKET, ABT_WALLET, PROSTOR_TICKET, PROSTOR_WALLET)';
COMMENT ON COLUMN abt_white_list_record_changes.type IS 'Тип подписки (ABT_TICKET, ABT_WALLET, PROSTOR_TICKET, PROSTOR_WALLET)';

-- Создание индекса для оптимизации запросов по типу
CREATE INDEX IF NOT EXISTS idx_abt_white_list_type ON abt_white_list(type);

-- Создание составного индекса для оптимизации запросов по типу и версии
CREATE INDEX IF NOT EXISTS idx_abt_white_list_type_version ON abt_white_list(type, version);