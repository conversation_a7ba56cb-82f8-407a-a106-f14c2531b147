-- Создание таблицы для метаданных белых списков
CREATE TABLE IF NOT EXISTS abt_white_list (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    hash_type INTEGER NOT NULL,
    version BIGINT NOT NULL,
    full_filename VARCHA<PERSON>(255),
    full_crc32 VARCHAR(8),
    diff_filename VARCHAR(255),
    diff_crc32 VARCHAR(8)
);

-- Создание таблицы для записей белых списков
CREATE TABLE IF NOT EXISTS abt_white_list_record (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    abonement_id VARCHAR(255),
    template_id VARCHAR(255),
    bitmap TEXT,
    bitmap_size INTEGER,
    uid VARCHAR(255) NOT NULL,
    hash VARCHAR(255) NOT NULL,
    hash_type INTEGER NOT NULL,
    version BIGINT NOT NULL,
    type VARCHAR(50),
    c_date TIMESTAMP,
    exp_date TIMESTAMP
);

-- Создание таблицы для отслеживания изменений в белых списках
CREATE TABLE IF NOT EXISTS abt_white_list_record_changes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    hash_type INTEGER NOT NULL,
    hash VARCHAR(255) NOT NULL,
    version BIGINT NOT NULL,
    record_id UUID,
    operation VARCHAR(20) NOT NULL,
    abonement_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Создание индексов для оптимизации запросов
CREATE INDEX IF NOT EXISTS idx_abt_white_list_project_hash_type ON abt_white_list(project_id, hash_type);
CREATE INDEX IF NOT EXISTS idx_abt_white_list_version ON abt_white_list(version);

CREATE INDEX IF NOT EXISTS idx_abt_white_list_record_project_version ON abt_white_list_record(project_id, version);
CREATE INDEX IF NOT EXISTS idx_abt_white_list_record_hash_hash_type ON abt_white_list_record(hash, hash_type);
CREATE INDEX IF NOT EXISTS idx_abt_white_list_record_abonement ON abt_white_list_record(abonement_id);
CREATE INDEX IF NOT EXISTS idx_abt_white_list_record_template ON abt_white_list_record(template_id);
CREATE INDEX IF NOT EXISTS idx_abt_white_list_record_c_date ON abt_white_list_record(c_date);
CREATE INDEX IF NOT EXISTS idx_abt_white_list_record_exp_date ON abt_white_list_record(exp_date);

CREATE INDEX IF NOT EXISTS idx_abt_white_list_record_changes_project_hash_type_version ON abt_white_list_record_changes(project_id, hash_type, version);
CREATE INDEX IF NOT EXISTS idx_abt_white_list_record_changes_hash ON abt_white_list_record_changes(hash);
CREATE INDEX IF NOT EXISTS idx_abt_white_list_record_changes_operation ON abt_white_list_record_changes(operation);

-- Добавление комментариев к таблицам
COMMENT ON TABLE abt_white_list IS 'Метаданные белых списков ABT';
COMMENT ON TABLE abt_white_list_record IS 'Записи белых списков ABT';
COMMENT ON TABLE abt_white_list_record_changes IS 'Изменения в белых списках ABT для формирования файлов';

-- Добавление комментариев к полям
COMMENT ON COLUMN abt_white_list.project_id IS 'Идентификатор проекта';
COMMENT ON COLUMN abt_white_list.hash_type IS 'Тип хэша (0=UID, 1=SHA256, 2=SHA1, 3=HMAC_SHA1, 4=HMAC_SHA256)';
COMMENT ON COLUMN abt_white_list.version IS 'Версия белого списка';
COMMENT ON COLUMN abt_white_list.full_filename IS 'Имя файла полного списка';
COMMENT ON COLUMN abt_white_list.full_crc32 IS 'CRC32 полного файла';
COMMENT ON COLUMN abt_white_list.diff_filename IS 'Имя файла diff';
COMMENT ON COLUMN abt_white_list.diff_crc32 IS 'CRC32 diff файла';

COMMENT ON COLUMN abt_white_list_record.project_id IS 'Идентификатор проекта';
COMMENT ON COLUMN abt_white_list_record.abonement_id IS 'Идентификатор абонемента';
COMMENT ON COLUMN abt_white_list_record.template_id IS 'Идентификатор шаблона';
COMMENT ON COLUMN abt_white_list_record.bitmap IS 'Данные абонемента в виде bitmap';
COMMENT ON COLUMN abt_white_list_record.bitmap_size IS 'Размер bitmap данных';
COMMENT ON COLUMN abt_white_list_record.uid IS 'Уникальный идентификатор';
COMMENT ON COLUMN abt_white_list_record.hash IS 'Хэш записи';
COMMENT ON COLUMN abt_white_list_record.hash_type IS 'Тип хэша';
COMMENT ON COLUMN abt_white_list_record.version IS 'Версия записи';
COMMENT ON COLUMN abt_white_list_record.type IS 'Тип подписки (ABT_TICKET, ABT_WALLET, PROSTOR_TICKET, PROSTOR_WALLET)';
COMMENT ON COLUMN abt_white_list_record.c_date IS 'Дата начала срока действия абонемента';
COMMENT ON COLUMN abt_white_list_record.exp_date IS 'Дата окончания срока действия абонемента';

COMMENT ON COLUMN abt_white_list_record_changes.project_id IS 'Идентификатор проекта';
COMMENT ON COLUMN abt_white_list_record_changes.hash_type IS 'Тип хэша';
COMMENT ON COLUMN abt_white_list_record_changes.hash IS 'Хэш записи';
COMMENT ON COLUMN abt_white_list_record_changes.version IS 'Версия изменения';
COMMENT ON COLUMN abt_white_list_record_changes.record_id IS 'Идентификатор записи из abt_white_list_record (null для DELETE)';
COMMENT ON COLUMN abt_white_list_record_changes.operation IS 'Тип операции (APPEND, CHANGE, DELETE)';
COMMENT ON COLUMN abt_white_list_record_changes.abonement_id IS 'Идентификатор абонемента'; 