-- Добавление полей для дат в таблицу abt_white_list_record
ALTER TABLE abt_white_list_record 
ADD COLUMN IF NOT EXISTS c_date TIMESTAMP,
ADD COLUMN IF NOT EXISTS exp_date TIMESTAMP;

-- Создание индексов для новых полей
CREATE INDEX IF NOT EXISTS idx_abt_white_list_record_c_date ON abt_white_list_record(c_date);
CREATE INDEX IF NOT EXISTS idx_abt_white_list_record_exp_date ON abt_white_list_record(exp_date);

-- Добавление комментариев к новым полям
COMMENT ON COLUMN abt_white_list_record.c_date IS 'Дата начала срока действия абонемента';
COMMENT ON COLUMN abt_white_list_record.exp_date IS 'Дата окончания срока действия абонемента'; 