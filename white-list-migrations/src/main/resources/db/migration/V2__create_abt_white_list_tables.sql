-- Создание таблиц для белых списков ABT

CREATE TABLE abt_white_list (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    project_id UUID NOT NULL,
    created_at TIMESTAMP DEFAULT now() NOT NULL,
    hash_type INTEGER NOT NULL,
    version BIGINT NOT NULL,
    full_filename VARCHAR(255),
    full_crc32 VARCHAR(8),
    diff_filename VARCHAR(255),
    diff_crc32 VARCHAR(8),
    PRIMARY KEY (id)
);

CREATE TABLE abt_white_list_record (
    id UUID DEFAULT gen_random_uuid() NOT NULL,
    project_id UUID NOT NULL,
    created_at TIMESTAMP DEFAULT now() NOT NULL,
    abonement_id VARCHAR(255) NOT NULL,
    template_id VARCHAR(255) NOT NULL,
    bitmap TEXT DEFAULT '',
    bitmap_size INTEGER DEFAULT 0,
    uid VARCHAR(255) NOT NULL,
    hash VARCHAR(255) NOT NULL,
    hash_type INTEGER NOT NULL,
    version BIGINT NOT NULL,
    type VARCHAR(255) NOT NULL,
    c_date TIMESTAMP,
    exp_date TIMESTAMP,
    PRIMARY KEY (id)
);

-- Создание индексов для оптимизации запросов
CREATE INDEX idx_abt_white_list_project_hash_version ON abt_white_list(project_id, hash_type, version);
CREATE INDEX idx_abt_white_list_record_project_version ON abt_white_list_record(project_id, version);
CREATE INDEX idx_abt_white_list_record_hash ON abt_white_list_record(hash);
CREATE INDEX idx_abt_white_list_record_abonement ON abt_white_list_record(abonement_id);

-- Комментарии к таблицам
COMMENT ON TABLE abt_white_list IS 'Метаданные белых списков ABT';
COMMENT ON TABLE abt_white_list_record IS 'Записи белых списков ABT';

-- Комментарии к полям
COMMENT ON COLUMN abt_white_list.hash_type IS 'Тип хэша: 0=UID, 1=SHA256, 2=SHA1, 3=HMAC_SHA1, 4=HMAC_SHA256';
COMMENT ON COLUMN abt_white_list_record.type IS 'Тип источника: 0=ABT_TK, 1=ABT_EMV, 2=PROSTOR';