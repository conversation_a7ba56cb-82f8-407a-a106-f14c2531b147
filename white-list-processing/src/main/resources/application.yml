spring:
  application:
    name: white-list-processing
  
  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://localhost:5432/white_list_db}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:password}
  
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092;localhost:9093}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:

      retries: 3
    abt_white_list_in_topic: ${KAFKA_ABT_WHITE_LIST_TOPIC:PRO.WL.ABT}
    abt_white_list_journal_topic: ${KAFKA_ABT_WHITE_LIST_JOURNAL_TOPIC:WL.JOURNAL.OUT}
    abt_white_list_processing_in_group: ${KAFKA_ABT_WHITE_LIST_GROUP:white-list-processing-group}
  
  flyway:
    enabled: ${DB_MIGRATION_ENABLE:true}
    validate-migration-naming: true
    url: jdbc:${DB_URL:postgresql://localhost:5438/white_list_db}
    user: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true

# AWS S3 конфигурация
s3:
  url: ${S3_URL:localhost:8080}
  access_key_id: ${S3_ACCESS_KEY_ID:fksdjfksd}
  secret_access_key: ${S3_SECRET_ACCESS_KEY:fsdfklsd}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-wl}

# Настройки diff файлов
diff:
  abt:
    enable: ${DIFF_ABT_ENABLE:false}

develop:
  stoplist_lib_enable_log: ${DEVELOP_WHITELIST_LIB_ENABLE_LOG:true}
  s3_disable_checksum: ${DEVELOP_S3_DISABLE_CHECKSUM:false}

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization

management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        liveness:
          include: '*'
        readiness:
          include: '*'