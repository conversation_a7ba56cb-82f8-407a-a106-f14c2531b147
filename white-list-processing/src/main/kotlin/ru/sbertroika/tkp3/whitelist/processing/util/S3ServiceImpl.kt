package ru.sbertroika.tkp3.whitelist.processing.util

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import java.io.InputStream

@Service
class S3ServiceImpl(
    private val s3Client: S3Client,
    @Value("\${s3.bucket}")
    private val bucket: String
) : S3Service {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun getFileContent(path: String, fileName: String): Either<Throwable, String> = Either.catch {
        val key = "$path/$fileName"
        logger.info("Loading file from S3: $key")
        
        val request = GetObjectRequest.builder()
            .bucket(bucket)
            .key(key)
            .build()
        
        val response: InputStream = s3Client.getObject(request)
        val content = response.readAllBytes().toString(Charsets.UTF_8)
        
        logger.info("Successfully loaded file from S3: $key, size: ${content.length}")
        content
    }.fold(
        { 
            logger.error("Error loading file from S3: $path/$fileName", it)
            it.left() 
        },
        { it.right() }
    )
} 