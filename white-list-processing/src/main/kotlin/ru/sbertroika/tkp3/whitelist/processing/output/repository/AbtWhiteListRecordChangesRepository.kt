package ru.sbertroika.tkp3.whitelist.processing.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.white.list.model.db.AbtWhiteListRecordChanges
import java.util.*

interface AbtWhiteListRecordChangesRepository : CoroutineCrudRepository<AbtWhiteListRecordChanges, UUID> {

    @Query("SELECT * from abt_white_list_record_changes where project_id = :projectId and type = :type and hash_type = :hashType and version = :version order by created_at")
    suspend fun findByProjectAndHashTypeAndVersion(projectId: UUID, type: String, hashType: Int, version: Long): List<AbtWhiteListRecordChanges>

    @Query("SELECT * from abt_white_list_record_changes where project_id = :projectId and hash_type = :hashType and version = :version and operation = :operation order by created_at")
    suspend fun findByProjectAndHashTypeAndVersionAndOperation(projectId: UUID, hashType: Int, version: Long, operation: String): List<AbtWhiteListRecordChanges>
} 