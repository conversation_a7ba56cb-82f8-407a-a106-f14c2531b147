package ru.sbertroika.tkp3.whitelist.processing

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories

@EnableR2dbcRepositories(
    basePackages = ["ru.sbertroika.tkp3.whitelist.processing.output.repository"]
)
@SpringBootApplication
open class WhiteListProcessingApplication

fun main(args: Array<String>) {
    runApplication<WhiteListProcessingApplication>(*args)
}