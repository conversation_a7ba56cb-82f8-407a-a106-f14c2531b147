package ru.sbertroika.tkp3.whitelist.processing.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import java.net.URI

@Configuration
open class S3Config(
    @Value("\${s3.access_key_id}")
    val awsId: String,
    @Value("\${s3.secret_access_key}")
    val awsKey: String,
    @Value("\${s3.region}")
    val region: String,
    @Value("\${s3.url}")
    val url: String,

    @Value("\${develop.s3_disable_checksum}")
    val s3DisableChecksum: Boolean
) {

    @Bean
    open fun s3Client(): S3Client {
        val credentials = AwsBasicCredentials.create(awsId, awsKey)
        return S3Client.builder()
            .endpointOverride(URI(url))
            .region(Region.of(region))
            .credentialsProvider(StaticCredentialsProvider.create(credentials))
            .apply {
                if (s3DisableChecksum) {
                    overrideConfiguration { builder ->
                        builder.addExecutionInterceptor(S3ChecksumDisablingInterceptor())
                    }
                }
            }

            .build()
    }
}