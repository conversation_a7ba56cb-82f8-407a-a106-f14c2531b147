package ru.sbertroika.tkp3.whitelist.processing.output

import arrow.core.Either
import ru.sbertroika.tkp3.whitelist.api.model.HashType
import ru.sbertroika.tkp3.whitelist.api.model.SubscriptionType
import java.util.*

interface WhiteListFileService {

    suspend fun makeAbtWhiteListFile(hashType: HashType, type: SubscriptionType, projectId: UUID, version: Long): Either<Throwable, WhiteListFile>

    suspend fun makeAbtWhiteListFileDiff(hashType: HashType, type: SubscriptionType, fullFileName: String, lastFullFileName: String, version: Long): Either<Throwable, WhiteListFile?>
}

data class WhiteListFile(
    val fileName: String,
    val crc32: String
) 