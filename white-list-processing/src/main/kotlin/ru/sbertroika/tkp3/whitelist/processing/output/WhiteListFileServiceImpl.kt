package ru.sbertroika.tkp3.whitelist.processing.output

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.whitelist.api.model.HashType
import ru.sbertroika.tkp3.whitelist.api.model.SubscriptionType
import ru.sbertroika.tkp3.whitelist.processing.output.repository.AbtWhiteListRecordRepository
import ru.sbertroika.tkp3.whitelist.processing.util.FileUtils
import ru.sbertroika.white.list.model.db.AbtWhiteList
import ru.sbertroika.white.list.model.db.AbtWhiteListRecord
import ru.sbertroika.white.list.model.db.AbtWhiteListRecordChanges
import ru.sbertroika.whitelist.Whitelist
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import java.io.ByteArrayInputStream
import java.io.File
import java.io.FileOutputStream
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*

@Service
class WhiteListFileServiceImpl(
    private val abtWhiteListRecordRepository: AbtWhiteListRecordRepository,
    private val whiteListLibraryService: WhiteListLibraryService,
    private val dbService: DBService,
    private val s3Client: S3Client,

    @Value("\${s3.bucket}")
    private val s3Bucket: String
) : WhiteListFileService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun makeAbtWhiteListFile(hashType: HashType, type: SubscriptionType, projectId: UUID, version: Long): Either<Throwable, WhiteListFile> = Either.catch {
        // Проверяем есть ли предыдущая версия белого списка
        val lastWhiteList = getLastWhiteList(hashType, type, projectId)
        val isNewFile = lastWhiteList == null
        
        val fileName = generateFileName(hashType, type, projectId, version)
        val localFilePath = "/tmp/$fileName"
        
        if (isNewFile) {
            // Создаем новый файл
            createNewWhiteListFile(hashType, type, projectId, version, localFilePath)
        } else {
            // Скачиваем предыдущую версию и применяем изменения
            val previousFilePath = downloadPreviousFile(lastWhiteList!!.fullFileName!!)
            applyChangesToFile(hashType, type, projectId, version, previousFilePath, localFilePath)
        }

        // Загружаем файл в S3
        uploadToS3(fileName, localFilePath)
        
        // Вычисляем CRC32
        val crc32 = calculateCrc32(localFilePath)
        
        // Сохраняем метаданные о новой версии белого списка
        saveWhiteListMetadata(hashType, projectId, version, type, fileName, crc32, null, null)
        
        // Очищаем записи изменений после успешного формирования файла
        clearChangesForVersion(projectId, type, hashType.ordinal, version)
        
        WhiteListFile(fileName, crc32)
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun makeAbtWhiteListFileDiff(hashType: HashType, type: SubscriptionType, fullFileName: String, lastFullFileName: String, version: Long): Either<Throwable, WhiteListFile?> = Either.catch {
        val diffFileName = generateFileName(hashType, type, UUID.randomUUID(), version, true)
        val localDiffFilePath = "/tmp/$diffFileName"
        
        // Скачиваем оба файла
        val currentFilePath = downloadFile(fullFileName)
        val previousFilePath = downloadFile(lastFullFileName)
        
        // Создаем diff файл
        whiteListLibraryService.acceptDiff(currentFilePath, previousFilePath)
        
        // Загружаем diff файл в S3
        uploadToS3(diffFileName, localDiffFilePath)
        
        // Вычисляем CRC32
        val crc32 = calculateCrc32(localDiffFilePath)
        
        // Обновляем метаданные, добавляя информацию о diff файле
        //TODO доработать
        updateWhiteListMetadataWithDiff(hashType, type, version, diffFileName, crc32)
        
        WhiteListFile(diffFileName, crc32)
    }

    private suspend fun createNewWhiteListFile(hashType: HashType, type: SubscriptionType, projectId: UUID, version: Long, localFilePath: String) {
        // Получаем изменения для версии
        val changes = getChangesForVersion(projectId, type, hashType.ordinal, version)
        val (typeWhiteList, isHash) = whiteListLibraryService.getWhiteListType(type, hashType)
        
        changes.forEach { change ->
            when (change.operation) {
                "APPEND" -> {
                    val record = getRecordById(change.recordId!!)
                    if (record != null) {
                        whiteListLibraryService.insertWhiteListItem(
                            localFilePath,
                            typeWhiteList,
                            record.hash!!,
                            record.abonementId!!,
                            record.templateId!!,
                            record.expDate?.toInstant()?.atZone(ZoneId.of("UTC")),
                            record.cDate?.toInstant()?.atZone(ZoneId.of("UTC")),
                            record.bitmap,
                            isHash
                        )
                    }
                }
                "CHANGE" -> {
                    val record = getRecordById(change.recordId!!)
                    if (record != null) {
                        whiteListLibraryService.updateWhiteListItem(
                            typeWhiteList,
                            localFilePath,
                            record.hash!!,
                            record.abonementId!!,
                            record.templateId!!,
                            record.expDate?.toInstant()?.atZone(ZoneId.of("UTC")),
                            record.cDate?.toInstant()?.atZone(ZoneId.of("UTC")),
                            record.bitmap,
                            isHash
                        )
                    }
                }
                "DELETE" -> {
                    // Для нового файла операции DELETE не выполняем
                    logger.debug("Skipping DELETE operation for new file: ${change.hash}")
                }
            }
        }

        // Выводим в лог информацию о состоянии файла после применения изменений
        Whitelist().showWhiteList(typeWhiteList.value, localFilePath)
    }

    private suspend fun applyChangesToFile(hashType: HashType, type: SubscriptionType, projectId: UUID, version: Long, previousFilePath: String, localFilePath: String) {
        // Копируем предыдущий файл
        copyFile(previousFilePath, localFilePath)
        
        // Получаем изменения для версии
        val changes = getChangesForVersion(projectId, type, hashType.ordinal, version)
        val (typeWhiteList, isHash) = whiteListLibraryService.getWhiteListType(type, hashType)

        changes.forEach { change ->
            when (change.operation) {
                "APPEND" -> {
                    val record = getRecordById(change.recordId!!)
                    if (record != null) {
                        whiteListLibraryService.insertWhiteListItem(
                            localFilePath,
                            typeWhiteList,
                            record.hash!!,
                            record.abonementId!!,
                            record.templateId!!,
                            record.expDate?.toInstant()?.atZone(ZoneId.of("UTC")),
                            record.cDate?.toInstant()?.atZone(ZoneId.of("UTC")),
                            record.bitmap,
                            isHash
                        )
                    }
                }
                "CHANGE" -> {
                    val record = getRecordById(change.recordId!!)
                    if (record != null) {
                        whiteListLibraryService.updateWhiteListItem(
                            typeWhiteList,
                            localFilePath,
                            record.hash!!,
                            record.abonementId!!,
                            record.templateId!!,
                            record.expDate?.toInstant()?.atZone(ZoneId.of("UTC")),
                            record.cDate?.toInstant()?.atZone(ZoneId.of("UTC")),
                            record.bitmap,
                            isHash
                        )
                    }
                }
                "DELETE" -> {
                    whiteListLibraryService.deleteWhiteListItem(
                        typeWhiteList,
                        localFilePath,
                        change.hash!!,
                        change.abonementId!!,
                        isHash
                    )
                }
            }
        }

        // Выводим в лог информацию о состоянии файла после применения изменений
        Whitelist().showWhiteList(typeWhiteList.value, localFilePath)
    }

    private fun generateFileName(hashType: HashType, sType: SubscriptionType, projectId: UUID, version: Long, isDiff: Boolean = false): String {
        val timestamp = ZonedDateTime.now(ZoneId.of("UTC")).format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"))
        val t = if (isDiff) "diff" else "full"
        return "${sType.name}_${hashType.name}_${projectId}_${version}_${t}_${timestamp}.bin"
    }

    private fun calculateCrc32(filePath: String): String {
        return FileUtils.calculateCrc32(filePath)
    }

    private fun uploadToS3(fileName: String, localFilePath: String) {
        val content = File(localFilePath).readBytes()
        
        val request = PutObjectRequest.builder()
            .bucket(s3Bucket)
            .key(fileName)
            .build()
        
        s3Client.putObject(request, RequestBody.fromInputStream(ByteArrayInputStream(content), content.size.toLong()))
        logger.info("Uploaded file to S3: $fileName")
    }

    // Реализованные вспомогательные методы
    private suspend fun getLastWhiteList(hashType: HashType, type: SubscriptionType, projectId: UUID): AbtWhiteList? {
        return dbService.getLastWhiteList(hashType, type, projectId).fold(
            { error ->
                logger.error("Error getting last white list for hashType: $hashType, projectId: $projectId", error)
                null
            },
            { it }
        )
    }

    private suspend fun getChangesForVersion(projectId: UUID, type: SubscriptionType, hashType: Int, version: Long): List<AbtWhiteListRecordChanges> {
        return dbService.getChangesForVersion(projectId, type, hashType, version).fold(
            { error ->
                logger.error("Error getting changes for version: projectId=$projectId, hashType=$hashType, version=$version", error)
                emptyList()
            },
            { it }
        )
    }

    private suspend fun getRecordById(recordId: UUID): AbtWhiteListRecord? {
        // Получаем запись по ID через repository
        return try {
            abtWhiteListRecordRepository.findById(recordId)
        } catch (e: Exception) {
            logger.error("Error getting record by ID: $recordId", e)
            null
        }
    }

    private fun downloadPreviousFile(fileName: String): String {
        val localPath = "/tmp/previous_$fileName"
        downloadFileFromS3(fileName, localPath)
        return localPath
    }

    private fun downloadFile(fileName: String): String {
        val localPath = "/tmp/$fileName"
        downloadFileFromS3(fileName, localPath)
        return localPath
    }

    private fun downloadFileFromS3(fileName: String, localPath: String) {
        try {
            logger.debug("Downloading file from S3: $fileName to $localPath")
            
            val request = GetObjectRequest.builder()
                .bucket(s3Bucket)
                .key(fileName)
                .build()
            
            val response = s3Client.getObject(request)
            val file = File(localPath)
            file.parentFile?.mkdirs()
            
            FileOutputStream(file).use { outputStream ->
                response.transferTo(outputStream)
            }
            
            logger.debug("Successfully downloaded file from S3: $fileName")
        } catch (e: Exception) {
            logger.error("Error downloading file from S3: $fileName", e)
            // Создаем пустой файл как fallback
            File(localPath).parentFile?.mkdirs()
            File(localPath).createNewFile()
        }
    }

    private fun copyFile(source: String, destination: String) {
        FileUtils.copyFile(source, destination)
    }

    private suspend fun clearChangesForVersion(projectId: UUID, type: SubscriptionType, hashType: Int, version: Long) {
        dbService.clearChangesForVersion(projectId, type, hashType, version).fold(
            { error ->
                logger.error("Error clearing changes for version: projectId=$projectId, hashType=$hashType, version=$version", error)
            },
            {
                logger.info("Successfully cleared changes for version: projectId=$projectId, hashType=$hashType, version=$version")
            }
        )
    }

    private suspend fun saveWhiteListMetadata(
        hashType: HashType,
        projectId: UUID,
        version: Long,
        subscriptionType: SubscriptionType,
        fullFileName: String,
        fullCrc32: String,
        diffFileName: String?,
        diffCrc32: String?
    ) {
        val whiteList = ru.sbertroika.white.list.model.db.AbtWhiteList(
            projectId = projectId,
            hashType = hashType.ordinal,
            version = version,
            type = subscriptionType.name,
            fullFileName = fullFileName,
            fullCrc32 = fullCrc32,
            diffFileName = diffFileName,
            diffCrc32 = diffCrc32
        )
        
        dbService.insertAbtWhiteList(whiteList).fold(
            { error ->
                logger.error("Error saving white list metadata for projectId: $projectId, hashType: $hashType, version: $version", error)
            },
            { saved ->
                logger.info("Successfully saved white list metadata: ${saved.id} with subscription type: ${subscriptionType.name}")
            }
        )
    }

    /**
     * Определяет строковое представление типа белого списка на основе HashType
     * @deprecated Используйте SubscriptionType вместо HashType для определения типа подписки
     */
    @Deprecated("Use SubscriptionType instead of HashType for subscription type determination")
    private fun getWhiteListTypeString(hashType: HashType): String {
        return when (hashType) {
            HashType.UID -> "MIFARE_BASED"
            HashType.SHA256 -> "PAN_SHA256_BASED"
            HashType.SHA1 -> "PAN_SHA1_BASED"
            HashType.HMAC_SHA1 -> "PAN_HMAC_SHA1_BASED"
            HashType.HMAC_SHA256 -> "PAN_HMAC_SHA256_BASED"
        }
    }

    private suspend fun updateWhiteListMetadataWithDiff(hashType: HashType, type: SubscriptionType, version: Long, diffFileName: String, diffCrc32: String) {
        //TODO Запись для обновления нужно искать и по проекту и по type
        dbService.updateWhiteListMetadata(hashType.ordinal, version, diffFileName, diffCrc32).fold(
            { error ->
                logger.error("Error updating white list metadata with diff for hashType: $hashType, version: $version", error)
            },
            { updated ->
                logger.info("Successfully updated white list metadata with diff: ${updated.id}")
            }
        )
    }
} 