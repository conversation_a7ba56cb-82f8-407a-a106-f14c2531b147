package ru.sbertroika.tkp3.whitelist.processing.util

import org.slf4j.LoggerFactory
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths
import java.util.zip.CRC32

object FileUtils {
    
    private val logger = LoggerFactory.getLogger(FileUtils::class.java)

    /**
     * Создает директорию если она не существует
     */
    fun ensureDirectoryExists(filePath: String) {
        val file = File(filePath)
        file.parentFile?.mkdirs()
    }

    /**
     * Создает пустой файл
     */
    fun createEmptyFile(filePath: String): Boolean {
        return try {
            ensureDirectoryExists(filePath)
            File(filePath).createNewFile()
            true
        } catch (e: Exception) {
            logger.error("Error creating empty file: $filePath", e)
            false
        }
    }

    /**
     * Копирует файл с обработкой ошибок
     */
    fun copyFile(source: String, destination: String): Boolean {
        return try {
            val sourceFile = File(source)
            val destFile = File(destination)
            
            if (!sourceFile.exists()) {
                logger.warn("Source file does not exist: $source, creating empty destination file")
                createEmptyFile(destination)
                return false
            }
            
            ensureDirectoryExists(destination)
            sourceFile.copyTo(destFile, overwrite = true)
            logger.debug("Successfully copied file from $source to $destination")
            true
        } catch (e: Exception) {
            logger.error("Error copying file from $source to $destination", e)
            createEmptyFile(destination)
            false
        }
    }

    /**
     * Вычисляет CRC32 файла
     */
    fun calculateCrc32(filePath: String): String {
        return try {
            val content = File(filePath).readBytes()
            val crc32 = CRC32()
            crc32.update(content)
            String.format("%08X", crc32.value)
        } catch (e: Exception) {
            logger.error("Error calculating CRC32 for file: $filePath", e)
            "00000000"
        }
    }

    /**
     * Проверяет существование файла
     */
    fun fileExists(filePath: String): Boolean {
        return File(filePath).exists()
    }

    /**
     * Получает размер файла в байтах
     */
    fun getFileSize(filePath: String): Long {
        return try {
            File(filePath).length()
        } catch (e: Exception) {
            logger.error("Error getting file size: $filePath", e)
            0L
        }
    }

    /**
     * Удаляет файл
     */
    fun deleteFile(filePath: String): Boolean {
        return try {
            File(filePath).delete()
        } catch (e: Exception) {
            logger.error("Error deleting file: $filePath", e)
            false
        }
    }

    /**
     * Очищает временные файлы старше указанного времени
     */
    fun cleanupTempFiles(tempDir: String, maxAgeHours: Long) {
        try {
            val tempPath = Paths.get(tempDir)
            if (!Files.exists(tempPath)) return
            
            val cutoffTime = System.currentTimeMillis() - (maxAgeHours * 60 * 60 * 1000)
            
            Files.walk(tempPath)
                .filter { Files.isRegularFile(it) }
                .filter { Files.getLastModifiedTime(it).toMillis() < cutoffTime }
                .forEach { path ->
                    try {
                        Files.delete(path)
                        logger.debug("Deleted old temp file: $path")
                    } catch (e: Exception) {
                        logger.warn("Could not delete old temp file: $path", e)
                    }
                }
        } catch (e: Exception) {
            logger.error("Error cleaning up temp files in: $tempDir", e)
        }
    }

    /**
     * Создает временную директорию
     */
    fun createTempDirectory(prefix: String): String {
        return try {
            val tempDir = Files.createTempDirectory(prefix)
            tempDir.toString()
        } catch (e: Exception) {
            logger.error("Error creating temp directory with prefix: $prefix", e)
            "/tmp/$prefix${System.currentTimeMillis()}"
        }
    }
} 