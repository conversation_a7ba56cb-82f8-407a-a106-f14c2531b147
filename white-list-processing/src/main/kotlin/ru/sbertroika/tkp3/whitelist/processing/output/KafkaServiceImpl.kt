package ru.sbertroika.tkp3.whitelist.processing.output

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.whitelist.processing.model.AbtWhiteListRecord
import ru.sbertroika.tkp3.whitelist.processing.util.mapper

@Service
class KafkaServiceImpl(
    private val kafkaTemplate: KafkaTemplate<String, String>,
    
    @Value("\${spring.kafka.abt_white_list_journal_topic}")
    private val journalTopic: String
) : KafkaService {

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val mapper = mapper()

    override suspend fun sendAbtWhiteListRecords(records: List<AbtWhiteListRecord>): Either<Throwable, Unit> = Either.catch {
        if (records.isEmpty()) {
            return@catch
        }
        
        records.forEach { record ->
            try {
                val message = mapper.writeValueAsString(record)
                kafkaTemplate.send(journalTopic, record.hash, message)
                logger.debug("Sent record to Kafka: ${record.hash}")
            } catch (e: Exception) {
                logger.error("Error sending record to Kafka: ${record.hash}", e)
                throw e
            }
        }
    }.fold(
        { it.left() },
        { Unit.right() }
    )
} 