package ru.sbertroika.tkp3.whitelist.processing.output

import arrow.core.Either
import ru.sbertroika.tkp3.whitelist.api.model.HashType
import ru.sbertroika.tkp3.whitelist.api.model.SubscriptionType
import ru.sbertroika.white.list.model.db.AbtWhiteList
import ru.sbertroika.white.list.model.db.AbtWhiteListRecord
import ru.sbertroika.white.list.model.db.AbtWhiteListRecordChanges
import ru.sbertroika.tkp3.whitelist.processing.model.AbtWhiteListRecord as ProcessingAbtWhiteListRecord
import java.util.*

interface DBService {
    suspend fun getAbtWhiteLists(hashType: HashType, projectId: UUID): Either<Throwable, List<AbtWhiteListRecord>>

    suspend fun getLastWhiteList(hashType: HashType, type: SubscriptionType, projectId: UUID): Either<Throwable, AbtWhiteList?>

    suspend fun saveAbtWhiteListRecord(record: AbtWhiteListRecord): Either<Throwable, AbtWhiteListRecord>

    suspend fun insertAbtWhiteListRecords(records: List<ProcessingAbtWhiteListRecord>): Either<Throwable, List<AbtWhiteListRecord>>

    suspend fun updateAbtWhiteListRecords(records: List<ProcessingAbtWhiteListRecord>): Either<Throwable, List<AbtWhiteListRecord>>

    suspend fun deleteAbtWhiteListRecords(records: List<ProcessingAbtWhiteListRecord>): Either<Throwable, List<AbtWhiteListRecord>>

    suspend fun insertAbtWhiteList(wl: AbtWhiteList): Either<Throwable, AbtWhiteList>

    suspend fun resetByProject(projectId: UUID): Either<Throwable, Unit>
    
    // Новые методы для работы с изменениями
    suspend fun findExistingRecord(projectId: UUID, hashType: Int, hash: String): Either<Throwable, AbtWhiteListRecord?>

    suspend fun saveChangeRecord(change: AbtWhiteListRecordChanges): Either<Throwable, AbtWhiteListRecordChanges>

    suspend fun getChangesForVersion(projectId: UUID, type: SubscriptionType, hashType: Int, version: Long): Either<Throwable, List<AbtWhiteListRecordChanges>>
    
    // Метод для очистки записей изменений после формирования файлов
    suspend fun clearChangesForVersion(projectId: UUID, type: SubscriptionType, hashType: Int, version: Long): Either<Throwable, Unit>
    
    // Метод для обновления метаданных с информацией о diff файле
    suspend fun updateWhiteListMetadata(hashType: Int, version: Long, diffFileName: String, diffCrc32: String): Either<Throwable, AbtWhiteList>
    
    // Методы для поиска белых списков по типу
    suspend fun findWhiteListsByType(type: String): Either<Throwable, List<AbtWhiteList>>

    suspend fun findWhiteListsByProjectAndType(projectId: UUID, type: String): Either<Throwable, List<AbtWhiteList>>

    suspend fun findWhiteListByTypeAndVersion(type: String, version: Long): Either<Throwable, AbtWhiteList?>
} 