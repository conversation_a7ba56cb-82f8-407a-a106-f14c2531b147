package ru.sbertroika.tkp3.whitelist.processing.input

import com.fasterxml.jackson.module.kotlin.readValue
import org.slf4j.LoggerFactory
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.whitelist.api.model.AbtWhiteList
import ru.sbertroika.tkp3.whitelist.api.model.HashType
import ru.sbertroika.tkp3.whitelist.api.model.OperationType
import ru.sbertroika.tkp3.whitelist.api.model.SubscriptionType
import ru.sbertroika.tkp3.whitelist.processing.model.AbtWhiteListRecord
import ru.sbertroika.tkp3.whitelist.processing.output.DBService
import ru.sbertroika.tkp3.whitelist.processing.output.KafkaService
import ru.sbertroika.tkp3.whitelist.processing.output.WhiteListFileService
import ru.sbertroika.tkp3.whitelist.processing.util.S3Service
import ru.sbertroika.tkp3.whitelist.processing.util.mapper
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*

@Service
class AbtWhiteListConsumer(
    private val dbService: DBService,
    private val kafkaService: KafkaService,
    private val whiteListFileService: WhiteListFileService,
    private val s3Service: S3Service
) {

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val mapper = mapper()

    @KafkaListener(
        topics = ["\${spring.kafka.abt_white_list_in_topic}"],
        groupId = "\${spring.kafka.abt_white_list_processing_in_group}"
    )
    suspend fun processAbtWhiteList(message: String) {
        try {
            logger.info("Received message: $message")
            
            val abtWhiteList: AbtWhiteList = mapper.readValue(message)
            logger.info("Processing AbtWhiteList for project: ${abtWhiteList.project}, version: ${abtWhiteList.version}")
            
            if (abtWhiteList.isReset) {
                logger.info("Resetting white list for project: ${abtWhiteList.project}")
                dbService.resetByProject(abtWhiteList.project).fold(
                    { error ->
                        logger.error("Error resetting white list for project: ${abtWhiteList.project}", error)
                        return
                    },
                    {
                        logger.info("Successfully reset white list for project: ${abtWhiteList.project}")
                    }
                )
            }
            
            val records = if (abtWhiteList.records.isNotEmpty()) {
                abtWhiteList.records
            } else if (abtWhiteList.path != null && abtWhiteList.srcFile != null) {
                loadRecordsFromS3(abtWhiteList.path!!, abtWhiteList.srcFile!!)
            } else {
                emptyList()
            }
            
            if (records.isNotEmpty()) {
                processRecordsByHashType(records, abtWhiteList.project, abtWhiteList.version)
            }
            
            logger.info("Successfully processed AbtWhiteList for project: ${abtWhiteList.project}")
            
        } catch (e: Exception) {
            logger.error("Error processing AbtWhiteList message", e)
        }
    }

    private suspend fun processRecordsByHashType(
        records: List<ru.sbertroika.tkp3.whitelist.api.model.AbtWhiteListOperationRecord>,
        projectId: UUID,
        version: Long
    ) {
        val recordsByHashType = records.groupBy { it.hashType }
        
        recordsByHashType.forEach { (hashType, typeRecords) ->
            val recordsBySubscriptionType = typeRecords.groupBy { it.type }
            recordsBySubscriptionType.forEach { (subscriptionType, typeRecords) ->
                logger.info("Processing ${typeRecords.size} records for hashType=$hashType and type=$subscriptionType")
                // Очищаем записи изменений после успешного формирования файла
                clearChangesForVersion(projectId, subscriptionType, hashType.ordinal, version)

                val appendRecords = typeRecords.filter { it.operation == OperationType.APPEND }
                val changeRecords = typeRecords.filter { it.operation == OperationType.CHANGE }
                val deleteRecords = typeRecords.filter { it.operation == OperationType.DELETE }

                // Обработка добавления записей
                if (appendRecords.isNotEmpty()) {
                    val processingRecords = appendRecords.map { record ->
                        toProcessingRecord(record, projectId, version)
                    }

                    dbService.insertAbtWhiteListRecords(processingRecords).fold(
                        { error ->
                            logger.error("Error inserting records for hash type: $hashType", error)
                        },
                        { insertedRecords ->
                            logger.info("Successfully inserted ${insertedRecords.size} records for hash type: $hashType")

                            // Отправка в журнал
                            val journalRecords = insertedRecords.map { dbRecord ->
                                toJournalRecord(dbRecord, OperationType.APPEND, version)
                            }
                            kafkaService.sendAbtWhiteListRecords(journalRecords)
                        }
                    )
                }

                // Обработка изменения записей
                if (changeRecords.isNotEmpty()) {
                    val processingRecords = changeRecords.map { record ->
                        toProcessingRecord(record, projectId, version)
                    }

                    dbService.updateAbtWhiteListRecords(processingRecords).fold(
                        { error ->
                            logger.error("Error updating records for hash type: $hashType", error)
                        },
                        { updatedRecords ->
                            logger.info("Successfully updated ${updatedRecords.size} records for hash type: $hashType")

                            // Отправка в журнал
                            val journalRecords = updatedRecords.map { dbRecord ->
                                toJournalRecord(dbRecord, OperationType.CHANGE, version)
                            }
                            kafkaService.sendAbtWhiteListRecords(journalRecords)
                        }
                    )
                }

                // Обработка удаления записей
                if (deleteRecords.isNotEmpty()) {
                    val processingRecords = deleteRecords.map { record ->
                        toProcessingRecord(record, projectId, version)
                    }

                    dbService.deleteAbtWhiteListRecords(processingRecords).fold(
                        { error ->
                            logger.error("Error deleting records for hash type: $hashType", error)
                        },
                        { deletedRecords ->
                            logger.info("Successfully deleted ${deletedRecords.size} records for hash type: $hashType")

                            // Отправка в журнал
                            val journalRecords = deletedRecords.map { dbRecord ->
                                toJournalRecord(dbRecord, OperationType.DELETE, version)
                            }
                            kafkaService.sendAbtWhiteListRecords(journalRecords)
                        }
                    )
                }

                // Генерация файла белого списка
                whiteListFileService.makeAbtWhiteListFile(hashType, subscriptionType, projectId, version).fold(
                    { error ->
                        logger.error("Error generating white list file for hash type: $hashType", error)
                    },
                    { file ->
                        logger.info("Successfully generated white list file: ${file.fileName}")

                        // Очищаем записи изменений после успешного формирования файла
                        clearChangesForVersion(projectId, subscriptionType, hashType.ordinal, version)
                    }
                )
            }
        }
    }

    private fun toProcessingRecord(
        record: ru.sbertroika.tkp3.whitelist.api.model.AbtWhiteListOperationRecord,
        projectId: UUID,
        version: Long
    ): AbtWhiteListRecord {
        return AbtWhiteListRecord(
            projectId = projectId,
            operation = record.operation,
            recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
            createdAt = if (record.operation == OperationType.APPEND) ZonedDateTime.now(ZoneId.of("UTC")) else null,
            deletedAt = if (record.operation == OperationType.DELETE) ZonedDateTime.now(ZoneId.of("UTC")) else null,
            abonementId = record.abonementId,
            templateId = record.templateId,
            bitmap = record.bitmap,
            bitmapSize = record.bitmapSize,
            uid = record.hash.take(16).uppercase(),
            hash = record.hash,
            hashType = record.hashType,
            createVersion = if (record.operation in listOf(OperationType.APPEND, OperationType.CHANGE)) version else null,
            deleteVersion = if (record.operation == OperationType.DELETE) version else null,
            type = record.type.name,
            cDate = record.cDate,
            expDate = record.expDate
        )
    }

    private fun toJournalRecord(
        dbRecord: ru.sbertroika.white.list.model.db.AbtWhiteListRecord,
        operationType: OperationType,
        version: Long
    ): AbtWhiteListRecord {
        return AbtWhiteListRecord(
            projectId = dbRecord.projectId,
            operation = operationType,
            recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
            createdAt = dbRecord.createdAt?.toInstant()?.atZone(ZoneId.of("UTC")),
            deletedAt = if (operationType == OperationType.DELETE) ZonedDateTime.now(ZoneId.of("UTC")) else null,
            abonementId = dbRecord.abonementId,
            templateId = dbRecord.templateId,
            bitmap = dbRecord.bitmap,
            bitmapSize = dbRecord.bitmapSize,
            uid = dbRecord.uid ?: "",
            hash = dbRecord.hash ?: "",
            hashType = HashType.values()[dbRecord.hashType ?: 0],
            createVersion = if (operationType in listOf(OperationType.APPEND, OperationType.CHANGE)) version else null,
            deleteVersion = if (operationType == OperationType.DELETE) version else null,
            type = dbRecord.type,
            cDate = dbRecord.cDate?.toInstant()?.atZone(ZoneId.of("UTC")),
            expDate = dbRecord.expDate?.toInstant()?.atZone(ZoneId.of("UTC"))
        )
    }

    private suspend fun loadRecordsFromS3(path: String, srcFile: String): List<ru.sbertroika.tkp3.whitelist.api.model.AbtWhiteListOperationRecord> {
        return s3Service.getFileContent(path, srcFile).fold(
            { error ->
                logger.error("Error loading records from S3: $path/$srcFile", error)
                emptyList()
            },
            { content ->
                try {
                    mapper.readValue(content)
                } catch (e: Exception) {
                    logger.error("Error parsing records from S3 content", e)
                    emptyList()
                }
            }
        )
    }

    private suspend fun clearChangesForVersion(projectId: UUID, type: SubscriptionType, hashType: Int, version: Long) {
        dbService.clearChangesForVersion(projectId, type, hashType, version).fold(
            { error ->
                logger.error("Error clearing changes for version: projectId=$projectId, hashType=$hashType, type=$type, version=$version", error)
            },
            {
                logger.info("Successfully cleared changes for version: projectId=$projectId, hashType=$hashType, type=$type, version=$version")
            }
        )
    }
} 