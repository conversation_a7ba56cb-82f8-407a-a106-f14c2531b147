package ru.sbertroika.tkp3.whitelist.processing.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.white.list.model.db.AbtWhiteListRecord
import java.util.*

interface AbtWhiteListRecordRepository : CoroutineCrudRepository<AbtWhiteListRecord, UUID> {

    @Query("SELECT * from abt_white_list_record where project_id = :projectId and version = :version")
    suspend fun findByProjectAndVersion(projectId: UUID, version: Long): List<AbtWhiteListRecord>

    @Query("SELECT * from abt_white_list_record where project_id = :projectId and hash = :hash and hash_type = :hashType")
    suspend fun findByProjectAndHashAndHashType(projectId: UUID, hash: String, hashType: Int): List<AbtWhiteListRecord>
} 