package ru.sbertroika.tkp3.whitelist.processing.model

import com.fasterxml.jackson.annotation.JsonFormat
import ru.sbertroika.tkp3.whitelist.api.model.HashType
import ru.sbertroika.tkp3.whitelist.api.model.OperationType
import java.time.ZonedDateTime
import java.util.*

data class AbtWhiteListRecord(
    val projectId: UUID?,
    val operation: OperationType,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val recordAt: ZonedDateTime?,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val createdAt: ZonedDateTime?,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val deletedAt: ZonedDateTime?,
    val abonementId: String?,
    val templateId: String?,
    val bitmap: String?,
    val bitmapSize: Int?,
    val uid: String,
    val hash: String,
    val hashType: HashType,
    val createVersion: Long?,
    val deleteVersion: Long?,
    val type: String?,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val cDate: ZonedDateTime? = null,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val expDate: ZonedDateTime? = null
) 