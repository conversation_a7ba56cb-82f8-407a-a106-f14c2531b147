package ru.sbertroika.tkp3.whitelist.processing.output

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.whitelist.api.model.HashType
import ru.sbertroika.tkp3.whitelist.api.model.SubscriptionType
import ru.sbertroika.whitelist.WhiteListType
import ru.sbertroika.whitelist.Whitelist
import java.time.ZonedDateTime

@Service
class WhiteListLibraryService {
    
    private val logger = LoggerFactory.getLogger(this::class.java)

    /**
     * Определяет тип белого списка на основе типа подписки и типа хэша
     */
    fun getWhiteListType(subscriptionType: SubscriptionType, hashType: HashType): Pair<WhiteListType, Boolean> {
        return when {
            // ABT_TICKET + UID
            subscriptionType == SubscriptionType.ABT_TICKET && hashType == HashType.UID -> 
                Pair(WhiteListType.MIFARE_SUBSCRIPTION_ENTRY, false)
            
            // ABT_WALLET + UID
            subscriptionType == SubscriptionType.ABT_WALLET && hashType == HashType.UID -> 
                Pair(WhiteListType.MIFARE_WALLET_ENTRY, false)
            
            // PROSTOR_TICKET/PROSTOR_WALLET + UID
            (subscriptionType == SubscriptionType.PROSTOR_TICKET || subscriptionType == SubscriptionType.PROSTOR_WALLET) && hashType == HashType.UID -> 
                Pair(WhiteListType.MIFARE_PROSTOR, false)
            
            // ABT_TICKET + SHA256
            subscriptionType == SubscriptionType.ABT_TICKET && hashType == HashType.SHA256 -> 
                Pair(WhiteListType.PAN_SHA256_SUBSCRIPTION, true)
            
            // ABT_WALLET + SHA256
            subscriptionType == SubscriptionType.ABT_WALLET && hashType == HashType.SHA256 -> 
                Pair(WhiteListType.PAN_SHA256_WALLET_ENTRY, false)
            
            // ABT_TICKET + SHA1
            subscriptionType == SubscriptionType.ABT_TICKET && hashType == HashType.SHA1 -> 
                Pair(WhiteListType.PAN_SHA1_WALLET_ENTRY, false)
            
            // ABT_WALLET + SHA1
            subscriptionType == SubscriptionType.ABT_WALLET && hashType == HashType.SHA1 -> 
                Pair(WhiteListType.PAN_SHA1_SUBSCRIPTION, false)
            
            else -> {
                logger.warn("Not implemented combination: subscriptionType=$subscriptionType, hashType=$hashType")
                Pair(WhiteListType.MIFARE_SUBSCRIPTION_ENTRY, false) // fallback
            }
        }
    }

    /**
     * Добавляет запись в файл белого списка
     */
    fun insertWhiteListItem(
        pathToFile: String,
        typeWhiteList: WhiteListType,
        panOrUid: String,
        abonementId: String,
        writeOffsId: String,
        expirationDateTime: ZonedDateTime?,
        creationDateTime: ZonedDateTime?,
        balance: String?,
        isHash: Boolean
    ) {
        //TODO Сделать проверка на валидность хэша
        try {
            val whitelist = Whitelist()
            whitelist.insertWhiteListItem(
                pathToFile,
                typeWhiteList.value,
                panOrUid,
                abonementId.toLongOrNull() ?: 0L,
                writeOffsId.toIntOrNull() ?: 0,
                expirationDateTime?.toInstant()?.epochSecond ?: 0L,
                creationDateTime?.toInstant()?.epochSecond ?: 0L,
                balance ?: "",
                isHash
            )
            logger.debug("Successfully inserted white list item: $panOrUid")
        } catch (e: Exception) {
            logger.error("Error inserting white list item: $panOrUid", e)
            throw e
        }
    }

    /**
     * Обновляет существующую запись в файле белого списка
     */
    fun updateWhiteListItem(
        typeWhiteList: WhiteListType,
        pathToFile: String,
        panOrUid: String,
        abonementId: String,
        writeOffsId: String,
        expirationDateTime: ZonedDateTime?,
        creationDateTime: ZonedDateTime?,
        balance: String?,
        isHash: Boolean
    ) {
        //TODO Сделать проверка на валидность хэша
        try {
            val whitelist = Whitelist()
            whitelist.updateWhiteListItem(
                typeWhiteList.value,
                pathToFile,
                panOrUid,
                abonementId.toLongOrNull() ?: 0L,
                writeOffsId.toIntOrNull() ?: 0,
                expirationDateTime?.toInstant()?.epochSecond ?: 0L,
                creationDateTime?.toInstant()?.epochSecond ?: 0L,
                balance ?: "",
                isHash
            )
            logger.debug("Successfully updated white list item: $panOrUid")
        } catch (e: Exception) {
            logger.error("Error updating white list item: $panOrUid", e)
            throw e
        }
    }

    /**
     * Удаляет запись из файла белого списка
     */
    fun deleteWhiteListItem(
        typeWhiteList: WhiteListType,
        pathToFile: String,
        panOrUid: String,
        abonementId: String,
        isHash: Boolean
    ) {
        //TODO Сделать проверка на валидность хэша
        try {
            val whitelist = Whitelist()
            whitelist.deleteWhiteListItem(
                typeWhiteList.value,
                pathToFile,
                panOrUid,
                abonementId.toLongOrNull() ?: 0L,
                isHash
            )
            logger.debug("Successfully deleted white list item: $panOrUid")
        } catch (e: Exception) {
            logger.error("Error deleting white list item: $panOrUid", e)
            throw e
        }
    }

    /**
     * Принимает diff файл
     */
    fun acceptDiff(
        pathToFile: String,
        pathToPreviousFile: String
    ) {
        try {
            val whitelist = Whitelist()
            whitelist.acceptDiff(pathToFile, pathToPreviousFile)
            logger.debug("Successfully accepted diff for file: $pathToFile")
        } catch (e: Exception) {
            logger.error("Error accepting diff for file: $pathToFile", e)
            throw e
        }
    }
} 