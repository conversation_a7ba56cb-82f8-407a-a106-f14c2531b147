package ru.sbertroika.tkp3.whitelist.processing.output

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.white.list.model.db.AbtWhiteList
import ru.sbertroika.white.list.model.db.AbtWhiteListRecord
import ru.sbertroika.white.list.model.db.AbtWhiteListRecordChanges
import ru.sbertroika.tkp3.whitelist.api.model.HashType
import ru.sbertroika.tkp3.whitelist.api.model.SubscriptionType
import ru.sbertroika.tkp3.whitelist.processing.model.AbtWhiteListRecord as ProcessingAbtWhiteListRecord
import ru.sbertroika.tkp3.whitelist.processing.output.repository.AbtWhiteListRepository
import ru.sbertroika.tkp3.whitelist.processing.output.repository.AbtWhiteListRecordRepository
import ru.sbertroika.tkp3.whitelist.processing.output.repository.AbtWhiteListRecordChangesRepository
import java.sql.Timestamp
import java.time.Instant
import java.util.*

@Service
class DBServiceImpl(
    private val abtWhiteListRepository: AbtWhiteListRepository,
    private val abtWhiteListRecordRepository: AbtWhiteListRecordRepository,
    private val abtWhiteListRecordChangesRepository: AbtWhiteListRecordChangesRepository
) : DBService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun getAbtWhiteLists(hashType: HashType, projectId: UUID): Either<Throwable, List<AbtWhiteListRecord>> = Either.catch {
        abtWhiteListRecordRepository.findByProjectAndHashAndHashType(projectId, "", hashType.ordinal)
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun getLastWhiteList(hashType: HashType, type: SubscriptionType, projectId: UUID): Either<Throwable, AbtWhiteList?> = Either.catch {
        abtWhiteListRepository.findLast(projectId, type.name, hashType.ordinal)
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun saveAbtWhiteListRecord(record: AbtWhiteListRecord): Either<Throwable, AbtWhiteListRecord> = Either.catch {
        abtWhiteListRecordRepository.save(record) ?: record
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun insertAbtWhiteListRecords(records: List<ProcessingAbtWhiteListRecord>): Either<Throwable, List<AbtWhiteListRecord>> = Either.catch {
        val savedRecords = mutableListOf<AbtWhiteListRecord>()
        
        records.forEach { processingRecord ->
            // Проверяем существующую запись
            val existingRecord = findExistingRecord(processingRecord.projectId!!, processingRecord.hashType.ordinal, processingRecord.hash).fold(
                { null },
                { it }
            )
            
            if (existingRecord != null) {
                // Обновляем существующую запись
                val updatedRecord = existingRecord.copy(
                    abonementId = processingRecord.abonementId,
                    templateId = processingRecord.templateId,
                    bitmap = processingRecord.bitmap,
                    bitmapSize = processingRecord.bitmapSize,
                    type = processingRecord.type,
                    cDate = processingRecord.cDate?.let { Timestamp.from(it.toInstant()) },
                    expDate = processingRecord.expDate?.let { Timestamp.from(it.toInstant()) },
                    version = processingRecord.createVersion ?: processingRecord.deleteVersion
                )
                
                val saved = abtWhiteListRecordRepository.save(updatedRecord)
                if (saved != null) {
                    savedRecords.add(saved)
                    
                    // Записываем изменение как UPDATE
                    saveChangeRecord(AbtWhiteListRecordChanges(
                        projectId = processingRecord.projectId,
                        hashType = processingRecord.hashType.ordinal,
                        hash = processingRecord.hash,
                        type = processingRecord.type,
                        version = processingRecord.createVersion ?: processingRecord.deleteVersion!!,
                        recordId = saved.id,
                        operation = "CHANGE",
                        abonementId = processingRecord.abonementId
                    ))
                }
            } else {
                // Создаем новую запись
                val newRecord = AbtWhiteListRecord(
                    projectId = processingRecord.projectId,
                    createdAt = Timestamp.from(Instant.now()),
                    abonementId = processingRecord.abonementId,
                    templateId = processingRecord.templateId,
                    bitmap = processingRecord.bitmap,
                    bitmapSize = processingRecord.bitmapSize,
                    uid = processingRecord.hash.take(16).uppercase(),
                    hash = processingRecord.hash,
                    hashType = processingRecord.hashType.ordinal,
                    version = processingRecord.createVersion ?: processingRecord.deleteVersion,
                    type = processingRecord.type,
                    cDate = processingRecord.cDate?.let { Timestamp.from(it.toInstant()) },
                    expDate = processingRecord.expDate?.let { Timestamp.from(it.toInstant()) }
                )
                
                val saved = abtWhiteListRecordRepository.save(newRecord)
                if (saved != null) {
                    savedRecords.add(saved)
                    
                    // Записываем изменение как APPEND
                    saveChangeRecord(AbtWhiteListRecordChanges(
                        projectId = processingRecord.projectId,
                        hashType = processingRecord.hashType.ordinal,
                        hash = processingRecord.hash,
                        type = processingRecord.type,
                        version = processingRecord.createVersion ?: processingRecord.deleteVersion!!,
                        recordId = saved.id,
                        operation = "APPEND",
                        abonementId = processingRecord.abonementId
                    ))
                }
            }
        }
        
        savedRecords
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun updateAbtWhiteListRecords(records: List<ProcessingAbtWhiteListRecord>): Either<Throwable, List<AbtWhiteListRecord>> = Either.catch {
        val updatedRecords = mutableListOf<AbtWhiteListRecord>()
        
        records.forEach { processingRecord ->
            val existingRecord = findExistingRecord(processingRecord.projectId!!, processingRecord.hashType.ordinal, processingRecord.hash).fold(
                { null },
                { it }
            )
            
            if (existingRecord != null) {
                val updatedRecord = existingRecord.copy(
                    abonementId = processingRecord.abonementId,
                    templateId = processingRecord.templateId,
                    bitmap = processingRecord.bitmap,
                    bitmapSize = processingRecord.bitmapSize,
                    type = processingRecord.type,
                    cDate = processingRecord.cDate?.let { Timestamp.from(it.toInstant()) },
                    expDate = processingRecord.expDate?.let { Timestamp.from(it.toInstant()) },
                    version = processingRecord.createVersion ?: processingRecord.deleteVersion
                )
                
                val saved = abtWhiteListRecordRepository.save(updatedRecord)
                if (saved != null) {
                    updatedRecords.add(saved)
                    
                    // Записываем изменение как UPDATE
                    saveChangeRecord(AbtWhiteListRecordChanges(
                        projectId = processingRecord.projectId,
                        hashType = processingRecord.hashType.ordinal,
                        hash = processingRecord.hash,
                        type = processingRecord.type,
                        version = processingRecord.createVersion ?: processingRecord.deleteVersion!!,
                        recordId = saved.id,
                        operation = "CHANGE",
                        abonementId = processingRecord.abonementId
                    ))
                }
            }
        }
        
        updatedRecords
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun deleteAbtWhiteListRecords(records: List<ProcessingAbtWhiteListRecord>): Either<Throwable, List<AbtWhiteListRecord>> = Either.catch {
        val deletedRecords = mutableListOf<AbtWhiteListRecord>()
        
        records.forEach { processingRecord ->
            val existingRecords = abtWhiteListRecordRepository.findByProjectAndHashAndHashType(
                processingRecord.projectId!!,
                processingRecord.hash,
                processingRecord.hashType.ordinal
            )
            
            existingRecords.forEach { existingRecord ->
                abtWhiteListRecordRepository.deleteById(existingRecord.id!!)
                deletedRecords.add(existingRecord)
                
                // Записываем изменение как DELETE
                saveChangeRecord(AbtWhiteListRecordChanges(
                    projectId = processingRecord.projectId,
                    hashType = processingRecord.hashType.ordinal,
                    hash = processingRecord.hash,
                    type = processingRecord.type,
                    version = processingRecord.deleteVersion!!,
                    recordId = null, // null для DELETE
                    operation = "DELETE",
                    abonementId = processingRecord.abonementId
                ))
            }
        }
        
        deletedRecords
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun insertAbtWhiteList(wl: AbtWhiteList): Either<Throwable, AbtWhiteList> = Either.catch {
        abtWhiteListRepository.save(wl) ?: wl
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun resetByProject(projectId: UUID): Either<Throwable, Unit> = Either.catch {
        // Удаляем все записи для проекта
        val records = abtWhiteListRecordRepository.findByProjectAndVersion(projectId, 0L)
        records.forEach { record ->
            abtWhiteListRecordRepository.deleteById(record.id!!)
        }
        
        // TODO: Реализовать удаление метаданных для проекта
        // Пока просто логируем
        logger.info("Reset completed for project: $projectId")
    }.fold(
        { it.left() },
        { Unit.right() }
    )

    override suspend fun findExistingRecord(projectId: UUID, hashType: Int, hash: String): Either<Throwable, AbtWhiteListRecord?> = Either.catch {
        val records = abtWhiteListRecordRepository.findByProjectAndHashAndHashType(projectId, hash, hashType)
        records.firstOrNull()
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun saveChangeRecord(change: AbtWhiteListRecordChanges): Either<Throwable, AbtWhiteListRecordChanges> = Either.catch {
        abtWhiteListRecordChangesRepository.save(change) ?: change
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun getChangesForVersion(projectId: UUID, type: SubscriptionType, hashType: Int, version: Long): Either<Throwable, List<AbtWhiteListRecordChanges>> = Either.catch {
        abtWhiteListRecordChangesRepository.findByProjectAndHashTypeAndVersion(projectId, type.name, hashType, version)
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun clearChangesForVersion(projectId: UUID, type: SubscriptionType, hashType: Int, version: Long): Either<Throwable, Unit> = Either.catch {
        val changes = abtWhiteListRecordChangesRepository.findByProjectAndHashTypeAndVersion(projectId, type.name, hashType, version)
        changes.forEach { change ->
            abtWhiteListRecordChangesRepository.deleteById(change.id!!)
        }
        logger.info("Cleared ${changes.size} change records for projectId: $projectId, hashType: $hashType, version: $version")
    }.fold(
        { it.left() },
        { Unit.right() }
    )

    override suspend fun updateWhiteListMetadata(hashType: Int, version: Long, diffFileName: String, diffCrc32: String): Either<Throwable, AbtWhiteList> = Either.catch {
        // Находим существующую запись метаданных
        val existingWhiteList = abtWhiteListRepository.findByHashTypeAndVersion(hashType, version)
            ?: throw IllegalStateException("White list metadata not found for hashType: $hashType, version: $version")
        
        // Обновляем информацию о diff файле (тип подписки не меняется)
        val updatedWhiteList = existingWhiteList.copy(
            diffFileName = diffFileName,
            diffCrc32 = diffCrc32
        )
        
        abtWhiteListRepository.save(updatedWhiteList) ?: updatedWhiteList
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun findWhiteListsByType(type: String): Either<Throwable, List<AbtWhiteList>> = Either.catch {
        abtWhiteListRepository.findByType(type)
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun findWhiteListsByProjectAndType(projectId: UUID, type: String): Either<Throwable, List<AbtWhiteList>> = Either.catch {
        abtWhiteListRepository.findByProjectAndType(projectId, type)
    }.fold(
        { it.left() },
        { it.right() }
    )

    override suspend fun findWhiteListByTypeAndVersion(type: String, version: Long): Either<Throwable, AbtWhiteList?> = Either.catch {
        abtWhiteListRepository.findByTypeAndVersion(type, version)
    }.fold(
        { it.left() },
        { it.right() }
    )
} 