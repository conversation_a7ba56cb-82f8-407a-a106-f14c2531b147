package ru.sbertroika.tkp3.whitelist.processing.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.white.list.model.db.AbtWhiteList
import java.util.*

interface AbtWhiteListRepository : CoroutineCrudRepository<AbtWhiteList, UUID> {

    @Query("SELECT * from abt_white_list where project_id = :projectId and type = :type and hash_type = :hashType order by version desc limit 1")
    suspend fun findLast(projectId: UUID, type: String, hashType: Int): AbtWhiteList?
    
    @Query("SELECT * from abt_white_list where hash_type = :hashType and version = :version limit 1")
    suspend fun findByHashTypeAndVersion(hashType: Int, version: Long): AbtWhiteList?
    
    @Query("SELECT * from abt_white_list where type = :type order by version desc")
    suspend fun findByType(type: String): List<AbtWhiteList>
    
    @Query("SELECT * from abt_white_list where project_id = :projectId and type = :type order by version desc")
    suspend fun findByProjectAndType(projectId: UUID, type: String): List<AbtWhiteList>
    
    @Query("SELECT * from abt_white_list where type = :type and version = :version limit 1")
    suspend fun findByTypeAndVersion(type: String, version: Long): AbtWhiteList?
} 