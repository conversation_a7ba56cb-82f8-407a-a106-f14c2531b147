package ru.sbertroika.tkp3.whitelist.processing.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import ru.sbertroika.whitelist.Whitelist
import kotlin.apply

@Configuration
open class WhiteListLibConfig(
    @Value("\${develop.stoplist_lib_enable_log}")
    val stopListLibEnableLog: Boolean
) {

    @Bean
    open fun whiteList(): Whitelist {
        return Whitelist().apply {
            if (stopListLibEnableLog) {
                initLib("", "0123456789abcdef", true, 5)
            }
        }
    }
}