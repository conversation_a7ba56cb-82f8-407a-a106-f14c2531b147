import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    idea
    `java-library`
    kotlin("jvm")
    `maven-publish`
    kotlin("plugin.spring") version libs.versions.kotlin.get() apply false
    id("org.springframework.boot") version "3.5.4"
    id("io.spring.dependency-management") version "1.1.0"
}

group = "ru.sbertroika.white-list"
version = rootProject.version

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}

repositories {
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
}

dependencies {
    implementation(project(":common-white-list"))
    implementation(project(":white-list-api"))
    implementation(project(":white-list-model"))
    implementation(project(":lib-white-list"))
    implementation("ru.sbertroika.common:common-api:1.0.7")

    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.springframework.boot:spring-boot-starter-data-r2dbc")
    implementation("org.springframework.kafka:spring-kafka:3.3.8")

    //Logging
    implementation("org.springframework.boot:spring-boot-starter-logging")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.8.1")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor:1.8.1")
    implementation("org.zalando:logbook-spring-boot-webflux-autoconfigure:3.10.0")
    implementation("org.zalando:logbook-okhttp:3.10.0")

    //Kotlin
    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlin.reflect)
    implementation("io.projectreactor.kotlin:reactor-kotlin-extensions")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.15.0")

    //Kotlin-ext
    implementation("io.arrow-kt:arrow-core:1.0.1")
    
    // Database migrations
    implementation(project(":white-list-migrations"))

    // Postgres
    runtimeOnly("org.postgresql:r2dbc-postgresql")
    runtimeOnly("io.r2dbc:r2dbc-pool")
    runtimeOnly("org.postgresql:postgresql:42.7.1")

    implementation("org.flywaydb:flyway-core:11.10.5")
    implementation("org.flywaydb:flyway-database-postgresql:11.10.5")

    implementation("software.amazon.awssdk:s3:2.22.0")

    //Test
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testCompileOnly("org.projectlombok:lombok")
    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        // Exclude the test engine you don't need
        exclude(group = "org.junit.vintage", module = "junit-vintage-engine")
    }
}

kotlin {
    jvmToolchain(17)
}

tasks.withType<KotlinCompile>().configureEach {
    compilerOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

tasks.withType<Test>().configureEach {
    val skipProvider = providers.gradleProperty("skipTest")
    if (!skipProvider.isPresent()) {
        useJUnitPlatform()
        testLogging {
            showStandardStreams = true
        }
    }
}

tasks.named<Jar>("jar") {
    enabled = false
} 